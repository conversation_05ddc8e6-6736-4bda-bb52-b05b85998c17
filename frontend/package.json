{"name": "novel-website-frontend", "version": "0.1.0", "private": true, "packageManager": "pnpm@9.4.0", "engines": {"node": ">=16.0.0", "pnpm": ">=8.0.0"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/material": "^6.3.1", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/lodash": "^4.17.17", "@types/node": "^16.18.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "axios": "^1.8.2", "clsx": "^2.1.0", "lodash": "^4.17.21", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.0", "styled-components": "^6.1.8", "typescript": "^4.9.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test --watchAll=false --env=jsdom --passWithNoTests", "test:watch": "react-scripts test --env=jsdom", "test:file": "react-scripts test --watchAll=false --env=jsdom --testPathPattern", "eject": "react-scripts eject", "type-check": "tsc --noEmit", "lint": "eslint src", "lint:fix": "eslint src --fix", "test:integration": "react-scripts test --env=jsdom --watchAll=false --testPathPattern=integration", "test:e2e": "playwright test", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "pnpm": {"overrides": {"postcss@<8.4.31": ">=8.4.31", "nth-check@<2.0.1": ">=2.0.1", "braces@<3.0.3": ">=3.0.3", "micromatch@<4.0.8": ">=4.0.8", "vue-template-compiler@>=2.0.0 <3.0.0": ">=3.0.0", "esbuild@<=0.24.2": ">=0.25.0", "webpack-dev-server@<=5.2.0": ">=5.2.1", "tar-fs@<3.0.9": ">=3.0.9", "ws@<8.17.1": ">=8.17.1", "cookie@<0.7.0": ">=0.7.0", "@babel/plugin-proposal-private-property-in-object": "npm:@babel/plugin-transform-private-property-in-object@^7.24.7"}}, "devDependencies": {"@lhci/cli": "^0.15.0", "@percy/cli": "^1.30.11", "@percy/storybook": "^6.0.4", "@playwright/test": "^1.42.1", "@storybook/addon-docs": "^9.0.12", "@storybook/addon-links": "^9.0.12", "@storybook/preset-create-react-app": "^9.0.12", "@storybook/preset-typescript": "^2.1.0", "@storybook/react-webpack5": "^9.0.12", "@storybook/testing-library": "0.2.0", "@tailwindcss/typography": "^0.5.10", "@testing-library/dom": "^10.4.0", "@types/jest": "^27.5.2", "@types/styled-components": "^5.1.34", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "autoprefixer": "^10.4.16", "eslint": "~8.57.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-storybook": "9.0.12", "identity-obj-proxy": "^3.0.0", "postcss": "^8.4.32", "react-scripts": "5.0.1", "storybook": "^9.0.12", "tailwindcss": "^3.3.6"}}