#!/bin/bash

# 端到端驗證測試腳本
# 驗證 Next.js 15 App Router 與 CRA 並行運行
# 測試 API 代理功能和 Monorepo 整合

set -euo pipefail

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
TIMEOUT_SECONDS=120
TEST_START_TIME=$(date +%s)

# 端口配置
CRA_PORT=3000
NEXTJS_PORT=3001
DJANGO_PORT=8000

# PID 追蹤
PIDS=()

# 日誌函數
log_info() {
    local elapsed=$(($(date +%s) - TEST_START_TIME))
    echo -e "${BLUE}[${elapsed}s]${NC} $1"
}

log_success() {
    local elapsed=$(($(date +%s) - TEST_START_TIME))
    echo -e "${GREEN}[${elapsed}s]${NC} $1"
}

log_warning() {
    local elapsed=$(($(date +%s) - TEST_START_TIME))
    echo -e "${YELLOW}[${elapsed}s]${NC} $1"
}

log_error() {
    local elapsed=$(($(date +%s) - TEST_START_TIME))
    echo -e "${RED}[${elapsed}s]${NC} $1"
}

# 清理函數
cleanup() {
    log_info "🧹 清理測試環境..."
    
    # 停止所有啟動的進程
    for pid in "${PIDS[@]}"; do
        if kill -0 "$pid" 2>/dev/null; then
            log_info "停止進程 $pid"
            kill "$pid" 2>/dev/null || true
        fi
    done
    
    # 等待進程結束
    sleep 2
    
    # 強制停止仍在運行的進程
    for pid in "${PIDS[@]}"; do
        if kill -0 "$pid" 2>/dev/null; then
            log_warning "強制停止進程 $pid"
            kill -9 "$pid" 2>/dev/null || true
        fi
    done
    
    log_success "✅ 清理完成"
}

# 設置陷阱
trap cleanup EXIT

# 檢查端口是否可用
check_port_available() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 1
    fi
    return 0
}

# 等待服務啟動
wait_for_service() {
    local port=$1
    local service_name=$2
    local max_attempts=30
    local attempt=0
    
    log_info "等待 $service_name 啟動 (端口 $port)..."
    
    while [[ $attempt -lt $max_attempts ]]; do
        if curl -s "http://localhost:$port" >/dev/null 2>&1; then
            log_success "✅ $service_name 已啟動"
            return 0
        fi
        
        attempt=$((attempt + 1))
        sleep 2
    done
    
    log_error "❌ $service_name 啟動超時"
    return 1
}

# 測試 API 端點
test_api_endpoint() {
    local url=$1
    local expected_status=$2
    local description=$3
    
    log_info "測試 $description: $url"
    
    local response
    local status_code
    
    response=$(curl -s -w "%{http_code}" "$url" 2>/dev/null || echo "000")
    status_code="${response: -3}"
    
    if [[ "$status_code" == "$expected_status" ]]; then
        log_success "✅ $description 測試通過 (狀態碼: $status_code)"
        return 0
    else
        log_error "❌ $description 測試失敗 (期望: $expected_status, 實際: $status_code)"
        return 1
    fi
}

# 主要測試函數
main() {
    log_info "🚀 開始端到端驗證測試..."
    
    # 檢查前置條件
    log_info "📋 檢查前置條件..."
    
    if [[ ! -f "package.json" ]]; then
        log_error "❌ 請在專案根目錄執行此腳本"
        exit 1
    fi
    
    if ! command -v pnpm >/dev/null 2>&1; then
        log_error "❌ pnpm 未安裝"
        exit 1
    fi
    
    if ! command -v curl >/dev/null 2>&1; then
        log_error "❌ curl 未安裝"
        exit 1
    fi
    
    # 檢查端口可用性
    log_info "🔍 檢查端口可用性..."
    
    if ! check_port_available $CRA_PORT; then
        log_error "❌ 端口 $CRA_PORT 已被佔用"
        exit 1
    fi
    
    if ! check_port_available $NEXTJS_PORT; then
        log_error "❌ 端口 $NEXTJS_PORT 已被佔用"
        exit 1
    fi
    
    log_success "✅ 端口檢查通過"
    
    # 安裝依賴
    log_info "📦 安裝依賴..."
    if ! pnpm install --frozen-lockfile >/dev/null 2>&1; then
        log_error "❌ 依賴安裝失敗"
        exit 1
    fi
    log_success "✅ 依賴安裝完成"
    
    # 建置應用
    log_info "🏗️ 建置應用..."
    if ! pnpm turbo build --filter=@novelwebsite/web --filter=@novelwebsite/web-next >/dev/null 2>&1; then
        log_error "❌ 應用建置失敗"
        exit 1
    fi
    log_success "✅ 應用建置完成"
    
    # 啟動 CRA 應用
    log_info "🚀 啟動 CRA 應用..."
    pnpm --filter @novelwebsite/web start >/dev/null 2>&1 &
    local cra_pid=$!
    PIDS+=($cra_pid)
    
    # 啟動 Next.js 應用
    log_info "🚀 啟動 Next.js 應用..."
    pnpm --filter @novelwebsite/web-next start >/dev/null 2>&1 &
    local nextjs_pid=$!
    PIDS+=($nextjs_pid)
    
    # 等待服務啟動
    if ! wait_for_service $CRA_PORT "CRA 應用"; then
        exit 1
    fi
    
    if ! wait_for_service $NEXTJS_PORT "Next.js 應用"; then
        exit 1
    fi
    
    # 執行端點測試
    log_info "🧪 執行端點測試..."
    
    local test_passed=0
    local test_total=0
    
    # 測試 CRA 應用
    test_total=$((test_total + 1))
    if test_api_endpoint "http://localhost:$CRA_PORT" "200" "CRA 應用首頁"; then
        test_passed=$((test_passed + 1))
    fi
    
    # 測試 Next.js 應用
    test_total=$((test_total + 1))
    if test_api_endpoint "http://localhost:$NEXTJS_PORT" "200" "Next.js 應用首頁"; then
        test_passed=$((test_passed + 1))
    fi
    
    test_total=$((test_total + 1))
    if test_api_endpoint "http://localhost:$NEXTJS_PORT/novels" "200" "Next.js 小說列表頁"; then
        test_passed=$((test_passed + 1))
    fi
    
    test_total=$((test_total + 1))
    if test_api_endpoint "http://localhost:$NEXTJS_PORT/about" "200" "Next.js 關於頁面"; then
        test_passed=$((test_passed + 1))
    fi
    
    test_total=$((test_total + 1))
    if test_api_endpoint "http://localhost:$NEXTJS_PORT/api-test" "200" "Next.js API 測試頁"; then
        test_passed=$((test_passed + 1))
    fi
    
    # 測試 API 代理 (如果 Django 後端可用)
    if curl -s "http://localhost:$DJANGO_PORT" >/dev/null 2>&1; then
        log_info "🔗 Django 後端可用，測試 API 代理..."
        
        test_total=$((test_total + 1))
        if test_api_endpoint "http://localhost:$NEXTJS_PORT/api/proxy/health" "200" "API 代理健康檢查"; then
            test_passed=$((test_passed + 1))
        fi
    else
        log_warning "⚠️ Django 後端不可用，跳過 API 代理測試"
    fi
    
    # 計算總時間
    local total_time=$(($(date +%s) - TEST_START_TIME))
    
    # 輸出測試結果
    echo ""
    log_info "📊 測試結果統計:"
    echo "  ⏱️  總執行時間: ${total_time}s"
    echo "  🧪 測試通過: $test_passed/$test_total"
    echo "  📈 成功率: $((test_passed * 100 / test_total))%"
    echo ""
    
    if [[ $test_passed -eq $test_total ]]; then
        log_success "🎉 所有端到端測試通過！"
        exit 0
    else
        log_error "❌ 部分測試失敗 ($((test_total - test_passed))/$test_total)"
        exit 1
    fi
}

# 執行主函數
main "$@"
