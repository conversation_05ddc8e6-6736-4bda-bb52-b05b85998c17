#!/bin/bash

# CI 路徑檢查腳本 v2.0
# 防止 novel.wsgi 類似問題，自動掃描關鍵配置路徑
# 在 PR 時自動執行，確保所有路徑配置正確

set -euo pipefail

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日誌函數
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 檢查結果統計
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0
WARNING_CHECKS=0

# 記錄檢查結果
record_check() {
    local status=$1
    local message=$2

    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))

    case $status in
        "PASS")
            PASSED_CHECKS=$((PASSED_CHECKS + 1))
            log_success "$message"
            ;;
        "FAIL")
            FAILED_CHECKS=$((FAILED_CHECKS + 1))
            log_error "$message"
            ;;
        "WARN")
            WARNING_CHECKS=$((WARNING_CHECKS + 1))
            log_warning "$message"
            ;;
    esac
}

# 檢查 Django 設定模組路徑
check_django_settings() {
    log_info "🔍 檢查 Django 設定模組路徑..."

    local expected_settings="config.django_settings"
    local issues_found=0

    # 檢查 manage.py
    if [[ -f "backend/manage.py" ]]; then
        if grep -q "config\.django_settings" "backend/manage.py"; then
            record_check "PASS" "manage.py 使用正確的 config.django_settings"
        else
            record_check "WARN" "manage.py 中未找到明確的 settings 配置"
        fi
    else
        record_check "FAIL" "backend/manage.py 不存在"
        issues_found=$((issues_found + 1))
    fi

    # 檢查 wsgi.py
    if [[ -f "backend/config/wsgi.py" ]]; then
        if grep -q "config\.django_settings" "backend/config/wsgi.py"; then
            record_check "PASS" "wsgi.py 使用正確的 config.django_settings"
        else
            record_check "WARN" "wsgi.py 中未找到明確的 settings 配置"
        fi
    else
        record_check "FAIL" "backend/config/wsgi.py 不存在"
        issues_found=$((issues_found + 1))
    fi

    # 檢查 asgi.py
    if [[ -f "backend/config/asgi.py" ]]; then
        if grep -q "config\.django_settings" "backend/config/asgi.py"; then
            record_check "PASS" "asgi.py 使用正確的 config.django_settings"
        else
            record_check "WARN" "asgi.py 中未找到明確的 settings 配置"
        fi
    fi

    # 檢查 Docker 配置
    local docker_files=("Dockerfile" "infra/docker/backend.Dockerfile" "docker-compose.ci.yml")
    for docker_file in "${docker_files[@]}"; do
        if [[ -f "$docker_file" ]]; then
            if grep -q "config\.django_settings" "$docker_file"; then
                record_check "PASS" "$docker_file 使用正確的 config.django_settings"
            fi
        fi
    done

    # 檢查測試配置
    if [[ -f "backend/tests/conftest.py" ]]; then
        if grep -q "config\.django_settings" "backend/tests/conftest.py"; then
            record_check "PASS" "conftest.py 使用正確的 config.django_settings"
        fi
    fi

    return $issues_found
}

# 檢查 ROOT_URLCONF 配置
check_root_urlconf() {
    log_info "🔍 檢查 ROOT_URLCONF 配置..."

    local expected_urlconf="config.urls"
    local issues_found=0

    # 檢查 Django 設定檔
    if [[ -f "backend/config/django_settings.py" ]]; then
        if grep -q "config\.urls" "backend/config/django_settings.py"; then
            record_check "PASS" "django_settings.py 使用正確的 config.urls"
        else
            record_check "WARN" "django_settings.py 中未找到 ROOT_URLCONF 配置"
        fi
    else
        record_check "FAIL" "backend/config/django_settings.py 不存在"
        issues_found=$((issues_found + 1))
    fi

    # 檢查 urls.py 是否存在
    if [[ -f "backend/config/urls.py" ]]; then
        record_check "PASS" "config/urls.py 存在"
    else
        record_check "FAIL" "backend/config/urls.py 不存在"
        issues_found=$((issues_found + 1))
    fi

    return $issues_found
}

# 檢查 WSGI_APPLICATION 配置
check_wsgi_application() {
    log_info "🔍 檢查 WSGI_APPLICATION 配置..."

    local expected_wsgi="config.wsgi.application"
    local issues_found=0

    if [[ -f "backend/config/django_settings.py" ]]; then
        if grep -q "config\.wsgi\.application" "backend/config/django_settings.py"; then
            record_check "PASS" "django_settings.py 使用正確的 config.wsgi.application"
        else
            record_check "WARN" "django_settings.py 中未找到 WSGI_APPLICATION 配置"
        fi
    fi

    return $issues_found
}

# 檢查 Next.js 配置路徑
check_nextjs_paths() {
    log_info "🔍 檢查 Next.js 配置路徑..."

    local issues_found=0

    # 檢查 Next.js 配置檔
    if [[ -f "apps/web-next/next.config.js" ]]; then
        record_check "PASS" "Next.js 配置檔存在"

        # 檢查 API 代理配置
        if grep -q "NEXT_PUBLIC_API_URL" "apps/web-next/next.config.js"; then
            record_check "PASS" "Next.js API 代理配置正確"
        else
            record_check "WARN" "Next.js 中未找到 API 代理配置"
        fi
    else
        record_check "FAIL" "apps/web-next/next.config.js 不存在"
        issues_found=$((issues_found + 1))
    fi

    # 檢查 TypeScript 配置
    if [[ -f "apps/web-next/tsconfig.json" ]]; then
        record_check "PASS" "Next.js TypeScript 配置存在"
    else
        record_check "FAIL" "apps/web-next/tsconfig.json 不存在"
        issues_found=$((issues_found + 1))
    fi

    # 檢查共享套件路徑
    if [[ -d "packages/tailwind-config" ]]; then
        record_check "PASS" "共享 Tailwind 配置套件存在"
    else
        record_check "WARN" "packages/tailwind-config 不存在"
    fi

    if [[ -d "packages/typescript-config" ]]; then
        record_check "PASS" "共享 TypeScript 配置套件存在"
    else
        record_check "WARN" "packages/typescript-config 不存在"
    fi

    return $issues_found
}

# 檢查 Monorepo 配置
check_monorepo_config() {
    log_info "🔍 檢查 Monorepo 配置..."

    local issues_found=0

    # 檢查 pnpm-workspace.yaml
    if [[ -f "pnpm-workspace.yaml" ]]; then
        record_check "PASS" "pnpm-workspace.yaml 存在"

        if grep -q "apps/\*" "pnpm-workspace.yaml" && grep -q "packages/\*" "pnpm-workspace.yaml"; then
            record_check "PASS" "pnpm workspace 配置正確"
        else
            record_check "FAIL" "pnpm workspace 配置不完整"
            issues_found=$((issues_found + 1))
        fi
    else
        record_check "FAIL" "pnpm-workspace.yaml 不存在"
        issues_found=$((issues_found + 1))
    fi

    # 檢查 turbo.json
    if [[ -f "turbo.json" ]]; then
        record_check "PASS" "turbo.json 存在"

        if grep -q "build" "turbo.json" && grep -q "dev" "turbo.json"; then
            record_check "PASS" "Turborepo 任務配置正確"
        else
            record_check "FAIL" "Turborepo 任務配置不完整"
            issues_found=$((issues_found + 1))
        fi
    else
        record_check "FAIL" "turbo.json 不存在"
        issues_found=$((issues_found + 1))
    fi

    return $issues_found
}

# 主函數
main() {
    log_info "🚀 開始 CI 路徑檢查..."
    echo ""

    # 檢查工作目錄
    if [[ ! -f "package.json" ]] || [[ ! -d "backend" ]]; then
        log_error "請在專案根目錄執行此腳本"
        exit 1
    fi

    # 執行各項檢查
    check_django_settings
    echo ""

    check_root_urlconf
    echo ""

    check_wsgi_application
    echo ""

    check_nextjs_paths
    echo ""

    check_monorepo_config
    echo ""

    # 輸出檢查結果統計
    log_info "📊 檢查結果統計:"
    echo "  總檢查項目: $TOTAL_CHECKS"
    echo "  ✅ 通過: $PASSED_CHECKS"
    echo "  ❌ 失敗: $FAILED_CHECKS"
    echo "  ⚠️  警告: $WARNING_CHECKS"
    echo ""

    # 判斷整體結果
    if [[ $FAILED_CHECKS -eq 0 ]]; then
        if [[ $WARNING_CHECKS -eq 0 ]]; then
            log_success "🎉 所有路徑檢查通過！"
            exit 0
        else
            log_warning "⚠️ 檢查通過，但有 $WARNING_CHECKS 個警告項目"
            exit 0
        fi
    else
        log_error "❌ 發現 $FAILED_CHECKS 個關鍵問題，請修復後重新檢查"
        exit 1
    fi
}

# 執行主函數
main "$@"
