# Tier 1.5 前端 Docker 映像 - 預安裝所有依賴
# NOTE: 這是過渡用映像，僅供歷史參考
#       生產與 CI 環境請改用 `frontend-tier2.Dockerfile`
# 目標：將測試時間從 2分5秒 降到 < 20秒

FROM node:18-alpine AS base

# 設置工作目錄
WORKDIR /workspace

# 安裝系統依賴
RUN apk add --no-cache git openssh-client

# 安裝 pnpm 並複製前端依賴配置
RUN npm install -g pnpm
COPY frontend/package.json frontend/pnpm-lock.yaml frontend/pnpm-workspace.yaml ./

# 安裝依賴 (這一步將被緩存在映像中)
RUN pnpm install --frozen-lockfile && \
    pnpm store prune

# 設置用戶權限 (與 EC2 user 一致)
RUN adduser -D -s /bin/sh ci-user
USER ci-user

# 設置環境變數
ENV NODE_ENV=production
ENV CI=true

# 健康檢查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD node --version || exit 1

# 標籤
LABEL maintainer="NovelWebsite DevOps Team"
LABEL version="1.0"
LABEL description="Frontend CI image with pre-installed dependencies"
LABEL tier="1.5"

# 預設命令
CMD ["sh"]
