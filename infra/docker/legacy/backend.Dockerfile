# Tier 1.5 後端 Docker 映像 - 預安裝所有依賴
# NOTE: 這是一個過渡用 Dockerfile，保留供歷史參考
#       生產與 CI 環境請改用 `backend-tier2.Dockerfile`
# 目標：將測試時間從 38秒 降到 < 10秒

FROM python:3.11-slim AS base

# 設置工作目錄
WORKDIR /workspace

# 安裝系統依賴
RUN apt-get update && apt-get install -y \
    git \
    gcc \
    g++ \
    libpq-dev \
    libxml2-dev \
    libxslt1-dev \
    libffi-dev \
    libssl-dev \
    && rm -rf /var/lib/apt/lists/*

# 升級 pip
RUN pip install --upgrade pip

# 🐍 確保 Python 與 pip 命令在所有路徑下可用
RUN ln -sf /usr/local/bin/python3 /usr/local/bin/python \
    && ln -sf /usr/local/bin/python3 /usr/bin/python \
    && ln -sf /usr/local/bin/python3 /usr/bin/python3 \
    && ln -sf /usr/local/bin/pip3 /usr/local/bin/pip \
    && ln -sf /usr/local/bin/pip3 /usr/bin/pip \
    && ln -sf /usr/local/bin/python3 /usr/local/bin/Python \
    && ln -sf /usr/local/bin/python3 /usr/bin/Python \
    && python3 --version && python --version && Python --version && pip --version

# 複製 requirements.txt
COPY backend/requirements.txt ./

# 安裝 Python 依賴 (這一步將被緩存在映像中)
RUN pip install --no-cache-dir -r requirements.txt

# 安裝 Playwright browsers for Scrapy
RUN playwright install chromium && \
    playwright install-deps chromium

# 設置用戶權限 (與 EC2 user 一致)
RUN useradd -m -s /bin/bash ci-user && \
    chown -R ci-user:ci-user /workspace
USER ci-user

# 設置環境變數
ENV PYTHONPATH=/workspace/backend
ENV DJANGO_SETTINGS_MODULE=backend.settings.test
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# 健康檢查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python --version || exit 1

# 標籤
LABEL maintainer="NovelWebsite DevOps Team"
LABEL version="1.0"
LABEL description="Backend CI image with pre-installed dependencies"
LABEL tier="1.5"

# 預設命令
CMD ["bash"]
