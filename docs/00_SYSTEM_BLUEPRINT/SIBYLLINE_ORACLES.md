# 📜 SIBYLLINE_ORACLES.md - 西比拉神諭卷軸
## 智慧啟示錄 - AI 思維框架與決策聖經

> *「這不是一份普通的文檔，這是你用來啟發 (Inspire) 和教導 (Teach) AI 如何思考的神諭或經文。」*

---

### 📋 使用原則

**性質**: 啟發式 (Heuristic) - 決策框架、戰略問題和思維模型
**內容**: 教導 AI 如何思考的「神諭」- 是漁而非魚
**權威**: 核心思維作業系統，分析問題的智慧基礎
**更新時機**: 獲得新感悟和智慧時由總指揮豐富內容

---

# 🤖 AI 團隊戰略決策框架 (Mentor's Framework)

## 🧠 核心思維框架

### 1. 價值交付優先 (Value Delivery First)
**原則**: 每一行代碼都應該為用戶創造價值
- ❌ 不要: 過度工程化、完美主義、技術炫技
- ✅ 要做: 快速迭代、用戶反饋、漸進改進

**關鍵問題**:
- 這個功能/修復是否直接影響用戶體驗？
- 投入產出比是否合理？
- 是否有更簡單的解決方案？

### 2. 數據驅動決策 (Data-Driven Decisions)
**原則**: 用數據說話，而非憑感覺
- 追蹤關鍵指標（進度百分比、阻塞天數、PR 完成率）
- 基於趨勢而非快照做決策
- 承認並量化不確定性

**關鍵指標**:
- MVP 完成度（功能點/總功能點）
- 用戶旅程覆蓋率
- 技術債務累積速度
- CI 穩定性指標

### 3. 動態債務管理 (Dynamic Debt Management)
**原則**: 技術債務不是敵人，是戰略工具
- 有意識地承擔債務以加速交付
- 設定債務償還觸發條件
- 平衡短期收益與長期成本

---

## 🎯 決策原則體系

### 決策模型：「償還時機」vs「價值交付」

```
高價值交付需求 + 低債務影響 = 推遲償還
高價值交付需求 + 高債務影響 = 局部償還
低價值交付需求 + 高債務影響 = 立即償還
低價值交付需求 + 低債務影響 = 永不償還
```

### Now/Next/Later 框架

**Now (立即行動)**
- 阻塞 MVP 的問題
- 影響多人協作的問題
- 低成本高收益的改進

**Next (下個衝刺)**
- 重要但不緊急的重構
- 性能優化
- 開發體驗改進

**Later (記錄備查)**
- 理想但非必要的架構改進
- 長期技術升級
- 探索性技術研究

---

## 📊 分析方法論

### OODA 循環思維 (Observe-Orient-Decide-Act)

1. **觀察 (Observe)**: 收集全面數據
   - Git 狀態、Issue 狀態、PR 進度
   - 外部依賴健康度
   - 團隊速度指標

2. **定向 (Orient)**: 理解當前位置
   - 與計劃對比偏差
   - 識別瓶頸和依賴
   - 評估風險等級

3. **決策 (Decide)**: 制定行動方案
   - 優先級排序
   - 資源分配
   - 時間估算

4. **行動 (Act)**: 執行並反饋
   - 具體可執行步驟
   - 成功標準定義
   - 反饋機制設置

---

## ⚖️ 平衡智慧

### 永恆的平衡藝術

1. **速度 vs 質量**
   - MVP 階段：70% 速度，30% 質量
   - 成長階段：50% 速度，50% 質量
   - 成熟階段：30% 速度，70% 質量

2. **創新 vs 穩定**
   - 核心功能：穩定優先
   - 實驗功能：創新優先
   - 基礎設施：絕對穩定

3. **個人 vs 團隊**
   - 個人效率服從團隊效率
   - 知識共享優於個人英雄
   - 文檔化思考過程

---

## 🔮 預測與風險評估

### 風險評估矩陣

```
影響程度 ↑
高 | 立即處理 | 制定計劃 |
中 | 持續監控 | 接受風險 |
低 | 忽略     | 忽略     |
    低        中        高
    ← 發生概率
```

### 早期預警信號
- 連續 3 天無進展 → 重新評估方案
- CI 失敗率 >20% → 立即修復
- 依賴服務異常 → 啟動備用方案
- 團隊速度下降 >30% → 調整範圍

---

## 🌟 AI 需要持續思考的戰略問題

### 每日三問
1. **價值問**: 今天的工作如何讓用戶更開心？
2. **進度問**: 哪個任務的完成最能推進 MVP？
3. **風險問**: 什麼可能在明天變成阻塞？

### 每週深思
1. **架構問**: 當前架構能支撐 3 個月後的需求嗎？
2. **團隊問**: 知識是否在團隊中均勻分布？
3. **策略問**: 我們是否在正確的方向上？

### 每月反省
1. **假設問**: 哪些初始假設已被證明錯誤？
2. **學習問**: 這個月最大的教訓是什麼？
3. **進化問**: 我們的流程如何進化得更好？

---

## 💡 執行原則

### 1. 敏捷但不混亂 (Agile but not Chaotic)
- 有計劃的靈活性
- 記錄每個轉向的原因
- 保持核心目標不變

### 2. 務實而非教條 (Pragmatic not Dogmatic)
- 原則是指南而非枷鎖
- 根據具體情況調整
- 結果導向思維

### 3. 持續改進心態 (Continuous Improvement)
- 每個錯誤都是學習機會
- 小步快跑勝過大步慢走
- 慶祝進步而非完美

---

## 📝 決策檢查清單

在做重要決策前，請確認：
- [ ] 這個決策是否符合 MVP 核心目標？
- [ ] 是否有數據支持這個決策？
- [ ] 短期收益和長期成本是否平衡？
- [ ] 是否考慮了所有利益相關者？
- [ ] 失敗的成本是否可接受？
- [ ] 是否有 B 計劃？

---

## 🧩 卷五：Agent 協同與指令設計的藝術 (The Art of Agent Collaboration & Prompt Design)

### 第一章：指令的「適應性」原則 (The Principle of Adaptive Instruction)

#### 核心智慧
**指令的格式並非唯一，其最優形態取決於接收指令的 Agent 的類型和能力。** 對於不同類型的執行者，我們必須提供不同粒度和結構的「工作指令」。

#### Agent 類型二分法

**1. 增強型 Agent (Augmented Agents)** - e.g., `augment`, `cursor`
- **特點**: 擁有強大的、近乎完整的本地上下文理解能力；擅長處理信息密度高、結構較鬆散的長篇報告；能夠在龐大的上下文中自主推理並找到解決方案。
- **最優指令格式**: **深度分析報告 (Deep-Dive Analysis Report)**。一份包含所有分析細節（原子任務、依賴、策略、上下文文件）的、鉅細靡遺的 HTML 報告。這份報告本身就是一個完美的、富含上下文的「超級 Prompt」。

**2. 任務型 Agent (Task-Oriented Agents)** - e.g., `codex`, 獨立的 `Coder` scripts
- **特點**: 在一個相對隔離的環境中工作；上下文理解能力有限；擅長執行具體的、結構化的、原子化的指令。
- **最優指令格式**: **結構化工作包 (Structured Work Packages)**。將複雜任務分解為多個獨立、自包含的「PR 簡報」，每個簡報都有明確的、按步驟執行的指令和清晰的 DoD。

#### Task-Dispatcher 的執行原則
- `Task-Dispatcher`（智能分派中心）在接收到任務時，**必須**首先確定本次任務的「目標執行者」類型。
- **IF** 目標是 `augment`，**THEN** 生成詳細的 HTML 分析報告。
- **IF** 目標是 `codex`，**THEN** 生成結構化的「工作包」評論。

#### 實際應用
```bash
# 為增強型 Agent 生成深度報告
/task-dispatcher 133 augment

# 為任務型 Agent 生成工作包
/task-dispatcher 133 codex
```

#### 智慧洞察
- **信息過濾的悖論**: 對於增強型 Agent，信息過濾和聚合反而是有害的。它們需要的是原始的、未經過濾的、完整的上下文，這樣它們內部的推理引擎才能發揮最大效用。
- **適應性系統設計**: 真正的智能系統不是追求唯一的「最優解」，而是能夠根據環境和執行者的不同，提供適應性的解決方案。
- **指令設計即系統架構**: 如何設計指令，本質上就是如何設計 AI 協作系統的架構。不同的指令格式決定了不同的協作模式。

---

## 🏗️ 卷六：架構遷移的黃金法則 (Golden Rules of Architecture Migration)

### 第一章：三大遷移戰略模式 (Three Migration Strategy Patterns)

#### 1. Strangler Fig Pattern - 絞殺榕模式 ✅ **黃金標準**

**核心智慧**: 在大型系統遷移中，「推倒重來」是高風險路徑，「並行運作，增量遷移」才是王道。

**實踐原則**:
- 創建全新的目標系統（如 `apps/web-next`）與舊系統（如 `apps/web`）並行存在
- 逐步將功能從舊系統遷移到新系統，舊系統繼續服務不受影響
- 將風險降至最低，確保在任何時刻都有可工作的系統

**關鍵決策指標**:
- 遷移過程中零停機時間
- 可隨時回滾到穩定狀態
- 團隊可以同時在新舊系統上並行開發

#### 2. Configuration as Code & Shared Packages - 配置即代碼，共享為王

**核心智慧**: Monorepo 的精髓在於統一標準和共享配置，從根本上杜絕配置漂移和重複勞動。

**實踐原則**:
- 建立共享配置包（如 `packages/tailwind-config`, `packages/typescript-config`）
- 所有應用（CRA, Next.js, 未來的框架）都使用統一的程式碼風格和類型定義
- 配置變更一次，全局生效

**長期價值**:
- 確保所有前端應用的一致性
- 新加入的開發者立即擁有正確的開發環境
- 維護成本隨應用數量增加而邊際遞減

#### 3. Backend for Frontend - BFF Proxy Pattern (防腐層)

**核心智慧**: 前端與後端的解耦不僅是技術問題，更是戰略架構問題。BFF Proxy 是「防腐層 (Anti-Corruption Layer)」的具體實現。

**實踐原則**:
- 設計 `app/api/proxy` 作為前後端之間的防腐層
- 前端（web-next）與後端具體實現（Django）徹底解耦
- 後端可以演進（Django → FastAPI → 微服務）而不影響前端

**戰略意義**:
- 技術棧選擇的自由度最大化
- 前後端團隊可以獨立演進
- 為未來的架構重構保留最大彈性

---

### 第二章：遷移決策的時機智慧 (Timing Wisdom for Migration Decisions)

#### 遷移觸發條件 (Migration Triggers)

1. **技術債務累積到臨界點**
   - 舊技術棧被官方棄用（如 Create React App）
   - 新功能開發受到技術限制阻礙
   - 維護成本超過重構成本

2. **業務需求推動**
   - SEO 需求推動 SSR/SSG 採用
   - 性能要求推動架構升級
   - 擴展性需求推動解耦設計

3. **團隊能力匹配**
   - 團隊具備新技術棧的專業能力
   - 有足夠時間進行漸進式遷移
   - 具備並行維護兩套系統的資源

#### 遷移風險控制原則

1. **最小可行遷移 (Minimum Viable Migration)**
   - 先遷移風險最低的功能模組
   - 建立完整的回滾機制
   - 確保每個階段都有可驗證的里程碑

2. **用戶體驗連續性**
   - 遷移過程對用戶完全透明
   - 功能特性保持一致
   - 性能不能倒退

3. **開發體驗平滑過渡**
   - 開發工具鏈提前統一
   - 代碼風格和規範保持一致
   - 文檔和知識轉移及時跟進

---

### 第三章：Monorepo 架構哲學 (Monorepo Architecture Philosophy)

#### 共享優於重複 (Sharing over Duplication)

**智慧核心**: 在 Monorepo 中，任何可以共享的東西都應該被抽象為共享包。

**實踐層級**:
1. **配置層共享**: ESLint, TypeScript, Tailwind CSS 配置
2. **組件層共享**: UI 組件庫、設計系統
3. **邏輯層共享**: 業務邏輯、API 客戶端、工具函數
4. **類型層共享**: TypeScript 類型定義、API 契約

#### 解耦優於耦合 (Decoupling over Coupling)

**智慧核心**: 即使在 Monorepo 中，也要保持適當的邊界和解耦。

**邊界劃分原則**:
- 按領域劃分包邊界（Domain-Driven Design）
- 按變化頻率劃分（快變 vs 慢變）
- 按團隊劃分（減少跨團隊依賴）

---

**🤖 Navigator 使用說明**:
- Navigator 在執行第二步時必須研習此卷軸
- 將所有智慧框架加載到思維系統中
- 在分析過程中應用這些框架和原則
- 在報告中展示如何應用卷軸智慧分析具體問題

**📝 總指揮使用說明**:
- 當獲得新的洞察或決策智慧時記錄於此
- 內容應該是可復用的思維模型而非具體指令
- 重點在於教會 AI 「如何思考」而非「思考什麼」
- 可隨時補充和完善，無需覆蓋既有智慧
