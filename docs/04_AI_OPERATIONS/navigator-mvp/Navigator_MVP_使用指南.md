# 🚀 Navigator MVP 衝刺系統使用指南

## 📖 系統簡介

Navigator MVP 是一個基於「法典 + 聖經」智慧系統的 AI 驅動專案管理工具，專為 NovelWebsite 的「黃金28」MVP 戰略設計。

### 🎯 核心理念

**遵循雙文件智慧系統**：
- **📜 MOSES_TABLETS.md (摩西石板)** - 最高法典：總指揮的絕對戰略指令
- **📜 SIBYLLINE_ORACLES.md (西比拉神諭)** - 智慧啟示錄：AI 思維框架與決策聖經

### 🤖 角色定位

1. **衝刺教練**: 不知疲倦地比對計劃與現實，識別瓶頸
2. **兼職架構師**: 審視技術債務和長期健康
3. **專案管理員**: 實時同步決策到 GitHub Projects

## 🛠️ 基本使用方式

### 標準分析模式

```bash
# 智能分析（檢查當日報告，自動判斷增量更新或完整生成）
./scripts/navigator-mvp.sh

# 強制完整重新分析（忽略當日已有報告）
./scripts/navigator-mvp.sh --update
```

### Issue 創建模式

```bash
# 創建 Navigator 格式的 Issue
./scripts/navigator-mvp.sh --create-issue --priority=P1 --context="實現用戶註冊功能"

# 不同優先級的 Issue 創建
./scripts/navigator-mvp.sh --create-issue --priority=P0 --context="修復登入系統安全漏洞"
./scripts/navigator-mvp.sh --create-issue --priority=S級 --context="重構爬蟲引擎架構"
```

### 幫助信息

```bash
# 查看使用說明
./scripts/navigator-mvp.sh --help
```

## 📊 輸出文件

### 1. HTML 衝刺簡報
- **路徑**: `docs/04_AI_OPERATIONS/navigator-mvp/YYYY-MM-DD_NovelWebsite_Navigator衝刺簡報.html`
- **內容**: 互動式進度儀表板、風險評估、行動建議
- **特色**: Mermaid 圖表、響應式設計、進度環動畫

### 2. 狀態檔案
- **路徑**: `docs/04_AI_OPERATIONS/navigator-mvp/navigator-state.json`
- **內容**: MVP 進度、風險趨勢、GitHub 狀態、外部服務狀況

### 3. GitHub Issues
- **自動創建**: 基於分析結果的關鍵 Issues
- **智能標籤**: 優先級、MVP類別、工作量評估
- **完整描述**: 包含執行流程和成功指標

## 🎯 核心功能

### 1. MVP 進度追蹤

- **整體完成度**: 基於 GitHub Issues 實際進度
- **黃金28內容**: 小說爬取進度（基於外部服務可用性）
- **5分鐘用戶旅程**: 核心功能完成情況

### 2. 動態風險評估

- **內容風險**: 基於外部服務（hjwzw.com）狀態
- **技術風險**: 基於阻塞 Issues 數量
- **時程風險**: 基於 P0 Issues 存在時間
- **資源風險**: 基於任務積壓情況

### 3. 智能行動建議

- **Top 3 Actions**: 基於風險評估和優先級
- **一句理由**: 每個建議都有明確的決策邏輯
- **動態調整**: 根據專案狀態變化調整建議

### 4. 專案管理整合

- **GitHub Issues 自動化**: 創建和更新關鍵 Issues
- **GitHub Projects 同步**: 如有權限，自動同步到看板
- **標籤系統**: 結構化的優先級和分類標籤

## 🧠 智慧決策系統

### 優先級邏輯

- **S級 - 戰略基石**: 影響6-12個月發展的架構性任務
- **P0 - 解除阻塞**: 立即解決阻塞MVP的問題
- **P1 - 推進核心**: 推進關鍵功能開發
- **P2 - 加速開發**: 提升開發效率
- **P3 - 並行優化**: 可並行處理的改進

### AI 工作量評估

- **small**: <4小時，快速修復或小型改進
- **medium**: 4小時-2天，標準功能開發
- **large**: >2天，複雜功能或架構性工作

### MVP 類別分類

- **黃金28內容**: 爬蟲、內容相關
- **5分鐘旅程**: 用戶體驗、前端功能
- **技術基礎**: 後端、CI/CD、架構

## 📈 增量更新機制

### 智能報告管理

- **當日報告檢測**: 自動檢查是否已有當日報告
- **增量更新**: 僅更新變更的部分，保持歷史連續性
- **變更檢測**: 基於 GitHub 狀態和外部服務變化
- **強制更新**: 使用 `--update` 參數跳過增量邏輯

### 狀態持久化

- **趨勢分析**: 比較當前與之前的風險水平
- **變化量化**: 具體的數值變化和趨勢箭頭
- **歷史記錄**: 保留分析歷史供對比

## 🔧 高級配置

### GitHub Projects 整合

```bash
# 如需 GitHub Projects 整合，執行：
gh auth refresh -h github.com -s project

# 系統會自動檢測權限並啟用完整同步模式
```

### 環境變量

- **TZ**: 設定為 'Asia/Taipei' (UTC+8)
- **PROJECT_ROOT**: 自動檢測專案根目錄

### 自定義配置

- **config檔案**: `docs/github-projects-config.json`（自動生成）
- **狀態檔案**: 可手動編輯進行微調

## 🚨 故障排除

### 常見問題

1. **標籤不存在**: 確保 GitHub repo 有正確的標籤設置
2. **權限不足**: 執行 `gh auth status` 檢查權限
3. **網路問題**: 檢查 hjwzw.com 可用性
4. **Git 狀態**: 確保在正確的分支和目錄

### 日誌檢查

- 系統會輸出詳細的執行日誌
- 錯誤會以紅色顯示
- 成功操作會以綠色顯示

## 🔄 最佳實踐

### 日常使用

1. **每日執行**: 建議每日運行一次獲取最新分析
2. **HTML 報告**: 用於站會和進度檢視
3. **Issue 管理**: 及時處理 Navigator 創建的 Issues
4. **趨勢監控**: 關注風險趨勢變化

### 團隊協作

1. **共享報告**: HTML 報告適合團隊共享
2. **Issue 分派**: 使用 Navigator 創建的 Issues 進行任務分派
3. **進度同步**: 定期檢查 MVP 進度變化
4. **風險溝通**: 基於風險評估進行團隊溝通

## 📚 相關文檔

- **系統架構**: [Navigator MVP 設計文檔]
- **摩西石板**: `docs/00_SYSTEM_BLUEPRINT/MOSES_TABLETS.md`
- **西比拉神諭**: `docs/00_SYSTEM_BLUEPRINT/SIBYLLINE_ORACLES.md`
- **技術債務**: `TECH_DEBT.md`
- **專案說明**: `README.md`

---

**🤖 Generated by Navigator MVP Sprint System v2.5**
**📅 更新日期**: 2025-06-28
**🎯 專案戰略**: 黃金28 - 讓用戶能在 5 分鐘內找到並開始閱讀一本喜歡的小說
