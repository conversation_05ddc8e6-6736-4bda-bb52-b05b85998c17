{"name": "@novelwebsite/web", "version": "0.1.0", "private": true, "packageManager": "pnpm@9.4.0", "engines": {"node": ">=16.0.0", "pnpm": ">=8.0.0"}, "homepage": ".", "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/material": "^6.3.1", "@testing-library/jest-dom": "^5.16.5", "@testing-library/user-event": "^14.6.1", "@types/lodash": "^4.17.17", "@types/node": "^16.18.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "axios": "^1.8.2", "clsx": "^2.1.0", "lodash": "^4.17.21", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.0", "styled-components": "^6.1.8", "typescript": "^4.9.5"}, "scripts": {"dev": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test --watchAll=false --ci --env=jsdom --passWithNoTests", "test:ci": "react-scripts test --watchAll=false --ci --env=jsdom --passWithNoTests", "test:watch": "react-scripts test --env=jsdom", "test:file": "react-scripts test --watchAll=false --env=jsdom --testPathPattern", "eject": "react-scripts eject", "type-check": "tsc --noEmit", "lint": "eslint src", "lint:fix": "eslint src --fix", "clean": "rm -rf build coverage storybook-static", "test:integration": "react-scripts test --env=jsdom --watchAll=false --testPathPattern=integration", "test:e2e": "playwright test", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@lhci/cli": "^0.15.0", "@percy/cli": "^1.30.11", "@percy/storybook": "^6.0.4", "@playwright/test": "^1.42.1", "@storybook/addon-docs": "^9.0.12", "@storybook/addon-links": "^9.0.12", "@storybook/preset-create-react-app": "^9.0.12", "@storybook/preset-typescript": "^2.1.0", "@storybook/react-webpack5": "^9.0.12", "@storybook/testing-library": "0.2.0", "@tailwindcss/typography": "^0.5.10", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^14.3.1", "@types/jest": "^27.5.2", "@types/styled-components": "^5.1.34", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "autoprefixer": "^10.4.16", "eslint": "~8.57.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-storybook": "9.0.12", "identity-obj-proxy": "^3.0.0", "postcss": "^8.4.32", "react-scripts": "5.0.1", "storybook": "^9.0.12", "tailwindcss": "^3.3.6", "webpack-dev-server": "^4.15.2"}}