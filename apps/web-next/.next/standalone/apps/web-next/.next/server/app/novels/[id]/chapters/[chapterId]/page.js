"use strict";(()=>{var e={};e.id=648,e.ids=[648],e.modules={846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3674:(e,t,s)=>{s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>h,pages:()=>d,routeModule:()=>x,tree:()=>c});var r=s(5406),a=s(1049),n=s(9073),i=s.n(n),l=s(430),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let c={children:["",{children:["novels",{children:["[id]",{children:["chapters",{children:["[chapterId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,4017)),"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/[id]/chapters/[chapterId]/page.tsx"]}]},{}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,4276)),"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,5559,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,7402,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,8467,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/[id]/chapters/[chapterId]/page.tsx"],h={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/novels/[id]/chapters/[chapterId]/page",pathname:"/novels/[id]/chapters/[chapterId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},3873:e=>{e.exports=require("path")},4017:(e,t,s)=>{s.r(t),s.d(t,{default:()=>p,generateMetadata:()=>u,revalidate:()=>x});var r=s(1862),a=s(2309),n=s.n(a),i=s(7909);let l={baseURL:process.env.NEXT_PUBLIC_API_URL||"http://localhost:8000/api/v1",timeout:3e4,retries:3};class o extends Error{constructor(e,t,s,r=!1){super(e),this.status=t,this.data=s,this.isRetryable=r,this.name="APIError"}static isRetryableError(e){return e>=500||!![408,429].includes(e)}getUserFriendlyMessage(){switch(this.status){case 400:return"請求參數錯誤，請檢查輸入內容";case 401:return"未授權訪問，請重新登入";case 403:return"權限不足，無法執行此操作";case 404:return"請求的資源不存在";case 408:return"請求超時，請稍後重試";case 429:return"請求過於頻繁，請稍後重試";case 500:return"伺服器內部錯誤，請稍後重試";case 502:return"網關錯誤，請稍後重試";case 503:return"服務暫時不可用，請稍後重試";case 504:return"網關超時，請稍後重試";default:return this.message||"發生未知錯誤"}}}async function c(e,t={}){let s,{method:r="GET",headers:a={},body:n,timeout:i=l.timeout,retries:d=l.retries}=t,h=e.startsWith("http")?e:`${l.baseURL}${e.startsWith("/")?e:`/${e}`}`,x={"Content-Type":"application/json",Accept:"application/json",...a};t.token&&(x.Authorization=`Bearer ${t.token}`),t.sessionCookie&&(x.Cookie=t.sessionCookie),t.isServerSide,x["X-Requested-With"]="SSR",n&&["POST","PUT","PATCH"].includes(r)&&(s="string"==typeof n?n:JSON.stringify(n));let u=null;for(let e=0;e<=d;e++)try{let e,t=new AbortController,a=setTimeout(()=>t.abort(),i),n=await fetch(h,{method:r,headers:x,body:s,signal:t.signal});clearTimeout(a);let l=n.headers.get("content-type");if(e=l?.includes("application/json")?await n.json():await n.text(),!n.ok){let t=o.isRetryableError(n.status);throw new o(`API request failed: ${n.statusText}`,n.status,e,t)}return{data:e,status:n.status,statusText:n.statusText,headers:n.headers}}catch(s){if(u=s,e===d)break;if(s instanceof TypeError||s instanceof DOMException){await new Promise(t=>setTimeout(t,1e3*(e+1)));continue}if(s instanceof o&&!s.isRetryable)throw s;let t=Math.min(1e3*Math.pow(2,e)+1e3*Math.random(),1e4);await new Promise(e=>setTimeout(e,t))}if(u instanceof o)throw u;throw new o(u?.message||"Unknown API error",0,u)}class d{static async get(e,t){return c(e,{...t,method:"GET"})}static async post(e,t,s){return c(e,{...s,method:"POST",body:t})}static async put(e,t,s){return c(e,{...s,method:"PUT",body:t})}static async delete(e,t){return c(e,{...t,method:"DELETE"})}static async patch(e,t,s){return c(e,{...s,method:"PATCH",body:t})}}let h={getChapterContent:(e,t)=>d.get(`/novels/${e}/chapters/${t}`)},x=60;async function u({params:e}){let{id:t,chapterId:s}=e;try{let e=(await h.getChapterContent(t,s)).data;return{title:`${e.title||`第 ${s} 章`} - 小說 ${t}`,description:`閱讀小說 ${t} 的第 ${s} 章內容`}}catch(e){return{title:`第 ${s} 章 - 小說 ${t}`,description:`閱讀小說 ${t} 的第 ${s} 章內容`}}}async function p({params:e}){let{id:t,chapterId:s}=e;(!t||!s||isNaN(Number(t))||isNaN(Number(s)))&&(0,i.notFound)();let a=null,l=null;try{a=(await h.getChapterContent(t,s)).data}catch(e){l=e,console.error("Failed to fetch chapter content:",e)}return l&&!a?(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"章節載入失敗"}),(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)(n(),{href:`/novels/${t}`,className:"text-blue-600 hover:text-blue-800 transition-colors",children:"← 返回小說"}),(0,r.jsx)(n(),{href:"/novels",className:"text-blue-600 hover:text-blue-800 transition-colors",children:"小說列表"})]})]}),(0,r.jsxs)("div",{className:"bg-red-50 p-6 rounded-lg border border-red-200",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-3 text-red-900",children:"❌ 無法載入章節內容"}),(0,r.jsxs)("p",{className:"text-red-800 mb-4",children:["抱歉，無法載入第 ",s," 章的內容。這可能是由於網路問題或章節不存在。"]}),(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)("button",{onClick:()=>window.location.reload(),className:"bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors",children:"重新載入"}),(0,r.jsx)(n(),{href:`/novels/${t}`,className:"bg-gray-200 text-gray-800 px-4 py-2 rounded-lg hover:bg-gray-300 transition-colors",children:"返回小說頁面"})]})]})]}):(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:a?.title||`第 ${s} 章`}),(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)(n(),{href:`/novels/${t}`,className:"text-blue-600 hover:text-blue-800 transition-colors",children:"← 返回小說"}),(0,r.jsx)(n(),{href:"/novels",className:"text-blue-600 hover:text-blue-800 transition-colors",children:"小說列表"})]})]}),(0,r.jsx)("div",{className:"bg-blue-50 p-4 rounded-lg border border-blue-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,r.jsx)("div",{className:"text-blue-800",children:"\uD83D\uDD04 ISR 啟用：每 60 秒重新驗證內容"}),(0,r.jsxs)("div",{className:"text-blue-600",children:["最後更新：",new Date().toLocaleString("zh-TW")]})]})}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border",children:[(0,r.jsxs)("div",{className:"p-6 border-b",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:a?.title||`第 ${s} 章`}),(0,r.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-500",children:[(0,r.jsxs)("span",{children:["小說 ID: ",t]}),(0,r.jsxs)("span",{children:["章節 ID: ",s]}),(0,r.jsxs)("span",{children:["字數: ",a?.wordCount||"未知"]}),(0,r.jsxs)("span",{children:["發布時間: ",a?.publishedAt||"未知"]})]})]}),(0,r.jsx)("div",{className:"p-6",children:a?.content?(0,r.jsx)("div",{className:"prose max-w-none",children:(0,r.jsx)("div",{className:"whitespace-pre-wrap leading-relaxed text-gray-800",children:a.content})}):(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("div",{className:"text-gray-500 mb-4",children:"\uD83D\uDCD6 這是示例章節內容"}),(0,r.jsxs)("div",{className:"max-w-2xl mx-auto text-gray-700 leading-relaxed",children:[(0,r.jsxs)("p",{className:"mb-4",children:["這是小說 ",t," 第 ",s," 章的示例內容。在實際應用中， 這裡會顯示從 Django 後端 API 獲取的真實章節內容。"]}),(0,r.jsx)("p",{className:"mb-4",children:"Next.js 15 App Router 的 ISR (Incremental Static Regeneration) 功能確保章節內容會定期更新，同時保持良好的性能。"}),(0,r.jsx)("p",{className:"mb-4",children:"當作者發布新章節或更新現有章節時，內容會在 60 秒內自動重新生成， 為讀者提供最新的閱讀體驗。"})]})]})})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between bg-white p-4 rounded-lg shadow-sm border",children:[(0,r.jsx)(n(),{href:`/novels/${t}/chapters/${Math.max(1,Number(s)-1)}`,className:`px-4 py-2 rounded-lg transition-colors ${1>=Number(s)?"bg-gray-100 text-gray-400 cursor-not-allowed":"bg-blue-600 text-white hover:bg-blue-700"}`,children:"← 上一章"}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"當前章節"}),(0,r.jsxs)("div",{className:"font-semibold text-gray-900",children:["第 ",s," 章"]})]}),(0,r.jsx)(n(),{href:`/novels/${t}/chapters/${Number(s)+1}`,className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:"下一章 →"})]}),(0,r.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg border text-sm text-gray-600",children:[(0,r.jsx)("h3",{className:"font-semibold mb-2",children:"ISR 技術特色："}),(0,r.jsxs)("ul",{className:"space-y-1",children:[(0,r.jsx)("li",{children:"• 伺服器端渲染 (SSR) 確保 SEO 友好"}),(0,r.jsx)("li",{children:"• 增量靜態再生 (ISR) 平衡性能與即時性"}),(0,r.jsx)("li",{children:"• 自動錯誤處理和降級顯示"}),(0,r.jsx)("li",{children:"• 支援手動重新驗證 API"})]})]})]})}},9121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[67,847,751,302],()=>s(3674));module.exports=r})();
