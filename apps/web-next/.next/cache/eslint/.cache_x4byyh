[{"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/about/page.tsx": "1", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api/proxy/[...path]/route.ts": "2", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api-test/page.tsx": "3", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/layout.tsx": "4", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/[id]/page.tsx": "5", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/page.tsx": "6", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/page.tsx": "7", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/lib/api.ts": "8"}, {"size": 5679, "mtime": 1751092647848, "results": "9", "hashOfConfig": "10"}, {"size": 4867, "mtime": 1751092725682, "results": "11", "hashOfConfig": "10"}, {"size": 7057, "mtime": 1751092797189, "results": "12", "hashOfConfig": "10"}, {"size": 2460, "mtime": 1751092349291, "results": "13", "hashOfConfig": "10"}, {"size": 6536, "mtime": 1751092678150, "results": "14", "hashOfConfig": "10"}, {"size": 2446, "mtime": 1751092620053, "results": "15", "hashOfConfig": "10"}, {"size": 4091, "mtime": 1751092371420, "results": "16", "hashOfConfig": "10"}, {"size": 6128, "mtime": 1751092753887, "results": "17", "hashOfConfig": "10"}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1cuyt3u", {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/about/page.tsx", [], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api/proxy/[...path]/route.ts", ["42", "43"], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api-test/page.tsx", ["44", "45", "46"], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/layout.tsx", [], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/[id]/page.tsx", [], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/page.tsx", [], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/page.tsx", [], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/lib/api.ts", ["47", "48", "49", "50", "51", "52", "53", "54", "55", "56", "57", "58", "59", "60"], [], {"ruleId": "61", "severity": 1, "message": "62", "line": 103, "column": 23, "nodeType": "63", "messageId": "64", "endLine": 103, "endColumn": 26, "suggestions": "65"}, {"ruleId": "66", "severity": 1, "message": "67", "line": 179, "column": 31, "nodeType": "68", "messageId": "69", "endLine": 179, "endColumn": 51}, {"ruleId": "61", "severity": 1, "message": "62", "line": 8, "column": 50, "nodeType": "63", "messageId": "64", "endLine": 8, "endColumn": 53, "suggestions": "70"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 11, "column": 61, "nodeType": "63", "messageId": "64", "endLine": 11, "endColumn": 64, "suggestions": "71"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 11, "column": 74, "nodeType": "63", "messageId": "64", "endLine": 11, "endColumn": 77, "suggestions": "72"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 18, "column": 19, "nodeType": "63", "messageId": "64", "endLine": 18, "endColumn": 22, "suggestions": "73"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 29, "column": 10, "nodeType": "63", "messageId": "64", "endLine": 29, "endColumn": 13, "suggestions": "74"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 35, "column": 27, "nodeType": "63", "messageId": "64", "endLine": 35, "endColumn": 30, "suggestions": "75"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 45, "column": 28, "nodeType": "63", "messageId": "64", "endLine": 45, "endColumn": 31, "suggestions": "76"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 161, "column": 24, "nodeType": "63", "messageId": "64", "endLine": 161, "endColumn": 27, "suggestions": "77"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 168, "column": 25, "nodeType": "63", "messageId": "64", "endLine": 168, "endColumn": 28, "suggestions": "78"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 168, "column": 55, "nodeType": "63", "messageId": "64", "endLine": 168, "endColumn": 58, "suggestions": "79"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 175, "column": 24, "nodeType": "63", "messageId": "64", "endLine": 175, "endColumn": 27, "suggestions": "80"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 175, "column": 54, "nodeType": "63", "messageId": "64", "endLine": 175, "endColumn": 57, "suggestions": "81"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 182, "column": 27, "nodeType": "63", "messageId": "64", "endLine": 182, "endColumn": 30, "suggestions": "82"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 189, "column": 26, "nodeType": "63", "messageId": "64", "endLine": 189, "endColumn": 29, "suggestions": "83"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 189, "column": 56, "nodeType": "63", "messageId": "64", "endLine": 189, "endColumn": 59, "suggestions": "84"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 201, "column": 37, "nodeType": "63", "messageId": "64", "endLine": 201, "endColumn": 40, "suggestions": "85"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 216, "column": 67, "nodeType": "63", "messageId": "64", "endLine": 216, "endColumn": 70, "suggestions": "86"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["87", "88"], "@typescript-eslint/no-unused-vars", "'request' is defined but never used.", "Identifier", "unusedVar", ["89", "90"], ["91", "92"], ["93", "94"], ["95", "96"], ["97", "98"], ["99", "100"], ["101", "102"], ["103", "104"], ["105", "106"], ["107", "108"], ["109", "110"], ["111", "112"], ["113", "114"], ["115", "116"], ["117", "118"], ["119", "120"], ["121", "122"], {"messageId": "123", "fix": "124", "desc": "125"}, {"messageId": "126", "fix": "127", "desc": "128"}, {"messageId": "123", "fix": "129", "desc": "125"}, {"messageId": "126", "fix": "130", "desc": "128"}, {"messageId": "123", "fix": "131", "desc": "125"}, {"messageId": "126", "fix": "132", "desc": "128"}, {"messageId": "123", "fix": "133", "desc": "125"}, {"messageId": "126", "fix": "134", "desc": "128"}, {"messageId": "123", "fix": "135", "desc": "125"}, {"messageId": "126", "fix": "136", "desc": "128"}, {"messageId": "123", "fix": "137", "desc": "125"}, {"messageId": "126", "fix": "138", "desc": "128"}, {"messageId": "123", "fix": "139", "desc": "125"}, {"messageId": "126", "fix": "140", "desc": "128"}, {"messageId": "123", "fix": "141", "desc": "125"}, {"messageId": "126", "fix": "142", "desc": "128"}, {"messageId": "123", "fix": "143", "desc": "125"}, {"messageId": "126", "fix": "144", "desc": "128"}, {"messageId": "123", "fix": "145", "desc": "125"}, {"messageId": "126", "fix": "146", "desc": "128"}, {"messageId": "123", "fix": "147", "desc": "125"}, {"messageId": "126", "fix": "148", "desc": "128"}, {"messageId": "123", "fix": "149", "desc": "125"}, {"messageId": "126", "fix": "150", "desc": "128"}, {"messageId": "123", "fix": "151", "desc": "125"}, {"messageId": "126", "fix": "152", "desc": "128"}, {"messageId": "123", "fix": "153", "desc": "125"}, {"messageId": "126", "fix": "154", "desc": "128"}, {"messageId": "123", "fix": "155", "desc": "125"}, {"messageId": "126", "fix": "156", "desc": "128"}, {"messageId": "123", "fix": "157", "desc": "125"}, {"messageId": "126", "fix": "158", "desc": "128"}, {"messageId": "123", "fix": "159", "desc": "125"}, {"messageId": "126", "fix": "160", "desc": "128"}, {"messageId": "123", "fix": "161", "desc": "125"}, {"messageId": "126", "fix": "162", "desc": "128"}, "suggestUnknown", {"range": "163", "text": "164"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "165", "text": "166"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "167", "text": "164"}, {"range": "168", "text": "166"}, {"range": "169", "text": "164"}, {"range": "170", "text": "166"}, {"range": "171", "text": "164"}, {"range": "172", "text": "166"}, {"range": "173", "text": "164"}, {"range": "174", "text": "166"}, {"range": "175", "text": "164"}, {"range": "176", "text": "166"}, {"range": "177", "text": "164"}, {"range": "178", "text": "166"}, {"range": "179", "text": "164"}, {"range": "180", "text": "166"}, {"range": "181", "text": "164"}, {"range": "182", "text": "166"}, {"range": "183", "text": "164"}, {"range": "184", "text": "166"}, {"range": "185", "text": "164"}, {"range": "186", "text": "166"}, {"range": "187", "text": "164"}, {"range": "188", "text": "166"}, {"range": "189", "text": "164"}, {"range": "190", "text": "166"}, {"range": "191", "text": "164"}, {"range": "192", "text": "166"}, {"range": "193", "text": "164"}, {"range": "194", "text": "166"}, {"range": "195", "text": "164"}, {"range": "196", "text": "166"}, {"range": "197", "text": "164"}, {"range": "198", "text": "166"}, {"range": "199", "text": "164"}, {"range": "200", "text": "166"}, [2391, 2394], "unknown", [2391, 2394], "never", [215, 218], [215, 218], [339, 342], [339, 342], [352, 355], [352, 355], [352, 355], [352, 355], [550, 553], [550, 553], [629, 632], [629, 632], [756, 759], [756, 759], [3384, 3387], [3384, 3387], [3574, 3577], [3574, 3577], [3604, 3607], [3604, 3607], [3778, 3781], [3778, 3781], [3808, 3811], [3808, 3811], [3987, 3990], [3987, 3990], [4182, 4185], [4182, 4185], [4212, 4215], [4212, 4215], [4449, 4452], [4449, 4452], [4805, 4808], [4805, 4808]]