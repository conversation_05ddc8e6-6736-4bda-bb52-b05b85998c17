[{"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/about/page.tsx": "1", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api/proxy/[...path]/route.ts": "2", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api/revalidate/route.ts": "3", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api-test/page.tsx": "4", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/layout.tsx": "5", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/[id]/chapters/[chapterId]/page.tsx": "6", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/[id]/page.tsx": "7", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/page.tsx": "8", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/page.tsx": "9", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/lib/api.ts": "10"}, {"size": 5654, "mtime": 1751094672669, "results": "11", "hashOfConfig": "12"}, {"size": 6595, "mtime": 1751094672671, "results": "13", "hashOfConfig": "12"}, {"size": 3998, "mtime": 1751094672669, "results": "14", "hashOfConfig": "12"}, {"size": 7014, "mtime": 1751094672669, "results": "15", "hashOfConfig": "12"}, {"size": 2460, "mtime": 1751092349291, "results": "16", "hashOfConfig": "12"}, {"size": 7624, "mtime": 1751094672669, "results": "17", "hashOfConfig": "12"}, {"size": 6529, "mtime": 1751094672670, "results": "18", "hashOfConfig": "12"}, {"size": 2444, "mtime": 1751094672667, "results": "19", "hashOfConfig": "12"}, {"size": 4071, "mtime": 1751094672667, "results": "20", "hashOfConfig": "12"}, {"size": 8103, "mtime": 1751094672671, "results": "21", "hashOfConfig": "12"}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "17x24ju", {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/about/page.tsx", [], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api/proxy/[...path]/route.ts", ["52", "53"], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api/revalidate/route.ts", [], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api-test/page.tsx", ["54", "55", "56"], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/layout.tsx", [], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/[id]/chapters/[chapterId]/page.tsx", ["57"], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/[id]/page.tsx", [], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/page.tsx", [], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/page.tsx", [], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/lib/api.ts", ["58", "59", "60", "61", "62", "63", "64", "65", "66", "67", "68", "69", "70", "71"], [], {"ruleId": "72", "severity": 1, "message": "73", "line": 175, "column": 23, "nodeType": "74", "messageId": "75", "endLine": 175, "endColumn": 26, "suggestions": "76"}, {"ruleId": "77", "severity": 1, "message": "78", "line": 251, "column": 31, "nodeType": null, "messageId": "79", "endLine": 251, "endColumn": 39}, {"ruleId": "72", "severity": 1, "message": "73", "line": 8, "column": 50, "nodeType": "74", "messageId": "75", "endLine": 8, "endColumn": 53, "suggestions": "80"}, {"ruleId": "72", "severity": 1, "message": "73", "line": 11, "column": 61, "nodeType": "74", "messageId": "75", "endLine": 11, "endColumn": 64, "suggestions": "81"}, {"ruleId": "72", "severity": 1, "message": "73", "line": 11, "column": 74, "nodeType": "74", "messageId": "75", "endLine": 11, "endColumn": 77, "suggestions": "82"}, {"ruleId": "77", "severity": 1, "message": "83", "line": 28, "column": 12, "nodeType": null, "messageId": "79", "endLine": 28, "endColumn": 17}, {"ruleId": "72", "severity": 1, "message": "73", "line": 18, "column": 19, "nodeType": "74", "messageId": "75", "endLine": 18, "endColumn": 22, "suggestions": "84"}, {"ruleId": "72", "severity": 1, "message": "73", "line": 70, "column": 10, "nodeType": "74", "messageId": "75", "endLine": 70, "endColumn": 13, "suggestions": "85"}, {"ruleId": "72", "severity": 1, "message": "73", "line": 81, "column": 27, "nodeType": "74", "messageId": "75", "endLine": 81, "endColumn": 30, "suggestions": "86"}, {"ruleId": "72", "severity": 1, "message": "73", "line": 91, "column": 28, "nodeType": "74", "messageId": "75", "endLine": 91, "endColumn": 31, "suggestions": "87"}, {"ruleId": "72", "severity": 1, "message": "73", "line": 227, "column": 24, "nodeType": "74", "messageId": "75", "endLine": 227, "endColumn": 27, "suggestions": "88"}, {"ruleId": "72", "severity": 1, "message": "73", "line": 234, "column": 25, "nodeType": "74", "messageId": "75", "endLine": 234, "endColumn": 28, "suggestions": "89"}, {"ruleId": "72", "severity": 1, "message": "73", "line": 234, "column": 55, "nodeType": "74", "messageId": "75", "endLine": 234, "endColumn": 58, "suggestions": "90"}, {"ruleId": "72", "severity": 1, "message": "73", "line": 241, "column": 24, "nodeType": "74", "messageId": "75", "endLine": 241, "endColumn": 27, "suggestions": "91"}, {"ruleId": "72", "severity": 1, "message": "73", "line": 241, "column": 54, "nodeType": "74", "messageId": "75", "endLine": 241, "endColumn": 57, "suggestions": "92"}, {"ruleId": "72", "severity": 1, "message": "73", "line": 248, "column": 27, "nodeType": "74", "messageId": "75", "endLine": 248, "endColumn": 30, "suggestions": "93"}, {"ruleId": "72", "severity": 1, "message": "73", "line": 255, "column": 26, "nodeType": "74", "messageId": "75", "endLine": 255, "endColumn": 29, "suggestions": "94"}, {"ruleId": "72", "severity": 1, "message": "73", "line": 255, "column": 56, "nodeType": "74", "messageId": "75", "endLine": 255, "endColumn": 59, "suggestions": "95"}, {"ruleId": "72", "severity": 1, "message": "73", "line": 267, "column": 37, "nodeType": "74", "messageId": "75", "endLine": 267, "endColumn": 40, "suggestions": "96"}, {"ruleId": "72", "severity": 1, "message": "73", "line": 282, "column": 67, "nodeType": "74", "messageId": "75", "endLine": 282, "endColumn": 70, "suggestions": "97"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["98", "99"], "@typescript-eslint/no-unused-vars", "'_request' is defined but never used.", "unusedVar", ["100", "101"], ["102", "103"], ["104", "105"], "'error' is defined but never used.", ["106", "107"], ["108", "109"], ["110", "111"], ["112", "113"], ["114", "115"], ["116", "117"], ["118", "119"], ["120", "121"], ["122", "123"], ["124", "125"], ["126", "127"], ["128", "129"], ["130", "131"], ["132", "133"], {"messageId": "134", "fix": "135", "desc": "136"}, {"messageId": "137", "fix": "138", "desc": "139"}, {"messageId": "134", "fix": "140", "desc": "136"}, {"messageId": "137", "fix": "141", "desc": "139"}, {"messageId": "134", "fix": "142", "desc": "136"}, {"messageId": "137", "fix": "143", "desc": "139"}, {"messageId": "134", "fix": "144", "desc": "136"}, {"messageId": "137", "fix": "145", "desc": "139"}, {"messageId": "134", "fix": "146", "desc": "136"}, {"messageId": "137", "fix": "147", "desc": "139"}, {"messageId": "134", "fix": "148", "desc": "136"}, {"messageId": "137", "fix": "149", "desc": "139"}, {"messageId": "134", "fix": "150", "desc": "136"}, {"messageId": "137", "fix": "151", "desc": "139"}, {"messageId": "134", "fix": "152", "desc": "136"}, {"messageId": "137", "fix": "153", "desc": "139"}, {"messageId": "134", "fix": "154", "desc": "136"}, {"messageId": "137", "fix": "155", "desc": "139"}, {"messageId": "134", "fix": "156", "desc": "136"}, {"messageId": "137", "fix": "157", "desc": "139"}, {"messageId": "134", "fix": "158", "desc": "136"}, {"messageId": "137", "fix": "159", "desc": "139"}, {"messageId": "134", "fix": "160", "desc": "136"}, {"messageId": "137", "fix": "161", "desc": "139"}, {"messageId": "134", "fix": "162", "desc": "136"}, {"messageId": "137", "fix": "163", "desc": "139"}, {"messageId": "134", "fix": "164", "desc": "136"}, {"messageId": "137", "fix": "165", "desc": "139"}, {"messageId": "134", "fix": "166", "desc": "136"}, {"messageId": "137", "fix": "167", "desc": "139"}, {"messageId": "134", "fix": "168", "desc": "136"}, {"messageId": "137", "fix": "169", "desc": "139"}, {"messageId": "134", "fix": "170", "desc": "136"}, {"messageId": "137", "fix": "171", "desc": "139"}, {"messageId": "134", "fix": "172", "desc": "136"}, {"messageId": "137", "fix": "173", "desc": "139"}, "suggestUnknown", {"range": "174", "text": "175"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "176", "text": "177"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "178", "text": "175"}, {"range": "179", "text": "177"}, {"range": "180", "text": "175"}, {"range": "181", "text": "177"}, {"range": "182", "text": "175"}, {"range": "183", "text": "177"}, {"range": "184", "text": "175"}, {"range": "185", "text": "177"}, {"range": "186", "text": "175"}, {"range": "187", "text": "177"}, {"range": "188", "text": "175"}, {"range": "189", "text": "177"}, {"range": "190", "text": "175"}, {"range": "191", "text": "177"}, {"range": "192", "text": "175"}, {"range": "193", "text": "177"}, {"range": "194", "text": "175"}, {"range": "195", "text": "177"}, {"range": "196", "text": "175"}, {"range": "197", "text": "177"}, {"range": "198", "text": "175"}, {"range": "199", "text": "177"}, {"range": "200", "text": "175"}, {"range": "201", "text": "177"}, {"range": "202", "text": "175"}, {"range": "203", "text": "177"}, {"range": "204", "text": "175"}, {"range": "205", "text": "177"}, {"range": "206", "text": "175"}, {"range": "207", "text": "177"}, {"range": "208", "text": "175"}, {"range": "209", "text": "177"}, {"range": "210", "text": "175"}, {"range": "211", "text": "177"}, [3908, 3911], "unknown", [3908, 3911], "never", [215, 218], [215, 218], [339, 342], [339, 342], [352, 355], [352, 355], [352, 355], [352, 355], [1471, 1474], [1471, 1474], [1639, 1642], [1639, 1642], [1766, 1769], [1766, 1769], [4963, 4966], [4963, 4966], [5153, 5156], [5153, 5156], [5183, 5186], [5183, 5186], [5357, 5360], [5357, 5360], [5387, 5390], [5387, 5390], [5566, 5569], [5566, 5569], [5761, 5764], [5761, 5764], [5791, 5794], [5791, 5794], [6028, 6031], [6028, 6031], [6384, 6387], [6384, 6387]]