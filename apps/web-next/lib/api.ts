/**
 * API 客戶端配置
 * 支援 Next.js 15 App Router 的 API 調用
 */

// API 配置
const API_CONFIG = {
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1',
  timeout: 30000, // 30 秒超時
  retries: 3, // 重試次數
}

// API 錯誤類型
export class APIError extends Error {
  constructor(
    message: string,
    public status: number,
    public data?: any
  ) {
    super(message)
    this.name = 'APIError'
  }
}

// 請求配置接口
interface RequestConfig {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  headers?: Record<string, string>
  body?: any
  timeout?: number
  retries?: number
}

// 響應接口
interface APIResponse<T = any> {
  data: T
  status: number
  statusText: string
  headers: Headers
}

/**
 * 基礎 API 請求函數
 */
async function request<T = any>(
  endpoint: string,
  config: RequestConfig = {}
): Promise<APIResponse<T>> {
  const {
    method = 'GET',
    headers = {},
    body,
    timeout = API_CONFIG.timeout,
    retries = API_CONFIG.retries,
  } = config

  // 構建完整 URL
  const url = endpoint.startsWith('http') 
    ? endpoint 
    : `${API_CONFIG.baseURL}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`

  // 準備請求標頭
  const requestHeaders: HeadersInit = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    ...headers,
  }

  // 準備請求體
  let requestBody: string | undefined
  if (body && ['POST', 'PUT', 'PATCH'].includes(method)) {
    requestBody = typeof body === 'string' ? body : JSON.stringify(body)
  }

  // 重試邏輯
  let lastError: Error | null = null
  
  for (let attempt = 0; attempt <= retries; attempt++) {
    try {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), timeout)

      const response = await fetch(url, {
        method,
        headers: requestHeaders,
        body: requestBody,
        signal: controller.signal,
      })

      clearTimeout(timeoutId)

      // 解析響應
      let responseData: T
      const contentType = response.headers.get('content-type')
      
      if (contentType?.includes('application/json')) {
        responseData = await response.json()
      } else {
        responseData = (await response.text()) as T
      }

      // 檢查響應狀態
      if (!response.ok) {
        throw new APIError(
          `API request failed: ${response.statusText}`,
          response.status,
          responseData
        )
      }

      return {
        data: responseData,
        status: response.status,
        statusText: response.statusText,
        headers: response.headers,
      }

    } catch (error) {
      lastError = error as Error
      
      // 如果是最後一次嘗試，拋出錯誤
      if (attempt === retries) {
        break
      }

      // 如果是網路錯誤或超時，等待後重試
      if (error instanceof TypeError || error instanceof DOMException) {
        await new Promise(resolve => setTimeout(resolve, 1000 * (attempt + 1)))
        continue
      }

      // 如果是 API 錯誤且狀態碼不適合重試，直接拋出
      if (error instanceof APIError && error.status < 500) {
        throw error
      }

      // 等待後重試
      await new Promise(resolve => setTimeout(resolve, 1000 * (attempt + 1)))
    }
  }

  // 拋出最後的錯誤
  if (lastError instanceof APIError) {
    throw lastError
  }

  throw new APIError(
    lastError?.message || 'Unknown API error',
    0,
    lastError
  )
}

/**
 * API 客戶端類
 */
export class APIClient {
  /**
   * GET 請求
   */
  static async get<T = any>(endpoint: string, config?: Omit<RequestConfig, 'method' | 'body'>) {
    return request<T>(endpoint, { ...config, method: 'GET' })
  }

  /**
   * POST 請求
   */
  static async post<T = any>(endpoint: string, data?: any, config?: Omit<RequestConfig, 'method'>) {
    return request<T>(endpoint, { ...config, method: 'POST', body: data })
  }

  /**
   * PUT 請求
   */
  static async put<T = any>(endpoint: string, data?: any, config?: Omit<RequestConfig, 'method'>) {
    return request<T>(endpoint, { ...config, method: 'PUT', body: data })
  }

  /**
   * DELETE 請求
   */
  static async delete<T = any>(endpoint: string, config?: Omit<RequestConfig, 'method' | 'body'>) {
    return request<T>(endpoint, { ...config, method: 'DELETE' })
  }

  /**
   * PATCH 請求
   */
  static async patch<T = any>(endpoint: string, data?: any, config?: Omit<RequestConfig, 'method'>) {
    return request<T>(endpoint, { ...config, method: 'PATCH', body: data })
  }
}

/**
 * 小說相關 API
 */
export const novelsAPI = {
  /**
   * 獲取小說列表
   */
  getList: (params?: Record<string, any>) => {
    const searchParams = params ? `?${new URLSearchParams(params).toString()}` : ''
    return APIClient.get(`/novels${searchParams}`)
  },

  /**
   * 獲取小說詳情
   */
  getDetail: (id: string | number) => {
    return APIClient.get(`/novels/${id}`)
  },

  /**
   * 獲取小說章節列表
   */
  getChapters: (novelId: string | number, params?: Record<string, any>) => {
    const searchParams = params ? `?${new URLSearchParams(params).toString()}` : ''
    return APIClient.get(`/novels/${novelId}/chapters${searchParams}`)
  },

  /**
   * 獲取章節內容
   */
  getChapterContent: (novelId: string | number, chapterId: string | number) => {
    return APIClient.get(`/novels/${novelId}/chapters/${chapterId}`)
  },
}

/**
 * 用戶相關 API
 */
export const userAPI = {
  /**
   * 用戶登入
   */
  login: (credentials: { username: string; password: string }) => {
    return APIClient.post('/auth/login', credentials)
  },

  /**
   * 用戶註冊
   */
  register: (userData: { username: string; email: string; password: string }) => {
    return APIClient.post('/auth/register', userData)
  },

  /**
   * 獲取用戶資料
   */
  getProfile: () => {
    return APIClient.get('/auth/profile')
  },

  /**
   * 登出
   */
  logout: () => {
    return APIClient.post('/auth/logout')
  },
}

// 預設導出
export default APIClient
