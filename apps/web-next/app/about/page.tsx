import { Metadata } from 'next'
import Link from 'next/link'

export const metadata: Metadata = {
  title: '關於我們',
  description: '了解 Next.js 15 App Router 版本的技術特色',
}

export default function AboutPage() {
  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">關於 Next.js 15 版本</h1>
        <Link 
          href="/"
          className="text-blue-600 hover:text-blue-800 transition-colors"
        >
          ← 返回首頁
        </Link>
      </div>

      <div className="prose max-w-none">
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg p-8 mb-8">
          <h2 className="text-2xl font-bold mb-4 text-white">技術架構升級</h2>
          <p className="text-blue-100 text-lg">
            這是使用 Next.js 15 App Router 重新構建的版本，與現有 CRA 應用並行運行，
            展示現代化前端架構的強大功能。
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8">
          <div className="space-y-6">
            <h3 className="text-2xl font-semibold text-gray-900">🚀 核心特色</h3>
            
            <div className="space-y-4">
              <div className="bg-white p-4 rounded-lg border">
                <h4 className="font-semibold text-gray-900 mb-2">App Router 架構</h4>
                <p className="text-gray-600 text-sm">
                  使用 Next.js 15 最新的 App Router，提供更好的開發體驗、
                  更強的類型安全和更靈活的佈局系統。
                </p>
              </div>

              <div className="bg-white p-4 rounded-lg border">
                <h4 className="font-semibold text-gray-900 mb-2">伺服器端渲染 (SSR)</h4>
                <p className="text-gray-600 text-sm">
                  支援 SSR 和 SSG，大幅提升 SEO 效果和首次載入速度，
                  解決 CRA 版本的 SEO 限制。
                </p>
              </div>

              <div className="bg-white p-4 rounded-lg border">
                <h4 className="font-semibold text-gray-900 mb-2">Monorepo 整合</h4>
                <p className="text-gray-600 text-sm">
                  完美整合 pnpm workspace 和 Turborepo，
                  支援共享套件和智能快取機制。
                </p>
              </div>
            </div>
          </div>

          <div className="space-y-6">
            <h3 className="text-2xl font-semibold text-gray-900">⚡ 性能優勢</h3>
            
            <div className="space-y-4">
              <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                <h4 className="font-semibold text-green-900 mb-2">自動程式碼分割</h4>
                <p className="text-green-800 text-sm">
                  Next.js 自動進行程式碼分割，只載入當前頁面需要的 JavaScript，
                  提升頁面載入速度。
                </p>
              </div>

              <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                <h4 className="font-semibold text-green-900 mb-2">圖片優化</h4>
                <p className="text-green-800 text-sm">
                  內建圖片優化功能，自動轉換為 WebP 格式，
                  支援響應式圖片和懶載入。
                </p>
              </div>

              <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                <h4 className="font-semibold text-green-900 mb-2">智能預載入</h4>
                <p className="text-green-800 text-sm">
                  自動預載入可見連結的頁面，提供近乎即時的頁面切換體驗。
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-yellow-50 p-6 rounded-lg border border-yellow-200 mt-8">
          <h3 className="text-xl font-semibold text-yellow-900 mb-3">🔄 並行運行策略</h3>
          <div className="text-yellow-800 space-y-2">
            <p>• <strong>CRA 版本</strong>: 運行在端口 3000，保持現有功能</p>
            <p>• <strong>Next.js 版本</strong>: 運行在端口 3001，展示新架構</p>
            <p>• <strong>Django 後端</strong>: 運行在端口 8000，兩個前端共享</p>
            <p>• <strong>漸進式遷移</strong>: 可以逐步將功能從 CRA 遷移到 Next.js</p>
          </div>
        </div>

        <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
          <h3 className="text-xl font-semibold text-blue-900 mb-3">🛠️ 開發工具</h3>
          <div className="grid md:grid-cols-2 gap-4 text-sm text-blue-800">
            <div>
              <h4 className="font-semibold mb-2">前端技術棧</h4>
              <ul className="space-y-1">
                <li>• Next.js 15.3.4</li>
                <li>• React 18.3.1</li>
                <li>• TypeScript 5.8.3</li>
                <li>• Tailwind CSS 3.4.17</li>
                <li>• Material-UI 6.4.12</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-2">開發工具</h4>
              <ul className="space-y-1">
                <li>• pnpm workspace</li>
                <li>• Turborepo 快取</li>
                <li>• ESLint + Prettier</li>
                <li>• 熱重載開發</li>
                <li>• 自動類型檢查</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
