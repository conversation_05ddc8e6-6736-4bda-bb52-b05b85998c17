import Link from 'next/link'
import { Metadata } from 'next'

export const metadata: Metadata = {
  title: '首頁',
  description: 'Next.js 15 App Router 首頁 - 與 CRA 應用並行運行',
}

export default function HomePage() {
  return (
    <div className="space-y-8">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg p-8">
        <h1 className="text-4xl font-bold mb-4">
          歡迎來到 Next.js 15 App Router 版本
        </h1>
        <p className="text-xl mb-6">
          這是與現有 CRA 應用並行運行的 Next.js 15 版本，展示 App Router 的強大功能
        </p>
        <div className="flex gap-4">
          <Link 
            href="/novels" 
            className="bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
          >
            瀏覽小說
          </Link>
          <Link 
            href="/about" 
            className="border border-white text-white px-6 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors"
          >
            了解更多
          </Link>
        </div>
      </section>

      {/* Features Section */}
      <section className="grid md:grid-cols-3 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h3 className="text-xl font-semibold mb-3 text-gray-900">
            🚀 App Router
          </h3>
          <p className="text-gray-600">
            使用 Next.js 15 最新的 App Router 架構，提供更好的開發體驗和性能
          </p>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h3 className="text-xl font-semibold mb-3 text-gray-900">
            ⚡ 伺服器端渲染
          </h3>
          <p className="text-gray-600">
            支援 SSR 和 SSG，提升 SEO 效果和頁面載入速度
          </p>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h3 className="text-xl font-semibold mb-3 text-gray-900">
            🔄 並行運行
          </h3>
          <p className="text-gray-600">
            與現有 CRA 應用並行運行，支援漸進式遷移策略
          </p>
        </div>
      </section>

      {/* Status Section */}
      <section className="bg-white p-6 rounded-lg shadow-sm border">
        <h2 className="text-2xl font-semibold mb-4 text-gray-900">
          系統狀態
        </h2>
        <div className="grid md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <h3 className="font-semibold text-gray-700">應用資訊</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Next.js 版本: 15.1.3</li>
              <li>• React 版本: 18.2.0</li>
              <li>• TypeScript 支援: ✅</li>
              <li>• Tailwind CSS: ✅</li>
              <li>• App Router: ✅</li>
            </ul>
          </div>
          <div className="space-y-2">
            <h3 className="font-semibold text-gray-700">Monorepo 整合</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• pnpm workspace: ✅</li>
              <li>• Turborepo 支援: ✅</li>
              <li>• 共享套件: 準備中</li>
              <li>• CI/CD 整合: 準備中</li>
              <li>• API 代理: 準備中</li>
            </ul>
          </div>
        </div>
      </section>

      {/* Development Info */}
      <section className="bg-blue-50 p-6 rounded-lg border border-blue-200">
        <h2 className="text-xl font-semibold mb-3 text-blue-900">
          開發資訊
        </h2>
        <div className="text-sm text-blue-800 space-y-1">
          <p>• 當前端口: 3001 (Next.js 15)</p>
          <p>• CRA 應用端口: 3000</p>
          <p>• Django 後端端口: 8000</p>
          <p>• 可使用 <code className="bg-blue-100 px-1 rounded">turbo dev</code> 同時啟動所有服務</p>
        </div>
      </section>
    </div>
  )
}
