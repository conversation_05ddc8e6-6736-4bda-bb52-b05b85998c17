import { Metadata } from 'next'
import Link from 'next/link'

export const metadata: Metadata = {
  title: '小說列表',
  description: '瀏覽所有可用的小說',
}

export default function NovelsPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">小說列表</h1>
        <Link 
          href="/"
          className="text-blue-600 hover:text-blue-800 transition-colors"
        >
          ← 返回首頁
        </Link>
      </div>

      <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
        <h2 className="text-xl font-semibold mb-3 text-blue-900">
          🚧 開發中
        </h2>
        <p className="text-blue-800 mb-4">
          這個頁面正在開發中。未來將整合 Django 後端 API 來顯示小說列表。
        </p>
        <div className="text-sm text-blue-700 space-y-1">
          <p>• 將使用 Next.js 15 的 Server Components</p>
          <p>• 支援 SSR 和 SSG 以提升 SEO</p>
          <p>• 整合現有的 Django API 端點</p>
          <p>• 保持與 CRA 版本的功能一致性</p>
        </div>
      </div>

      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* 示例小說卡片 */}
        {[1, 2, 3, 4, 5, 6].map((id) => (
          <div key={id} className="bg-white p-6 rounded-lg shadow-sm border hover:shadow-md transition-shadow">
            <div className="h-32 bg-gray-200 rounded-lg mb-4 flex items-center justify-center">
              <span className="text-gray-500">封面圖片</span>
            </div>
            <h3 className="text-lg font-semibold mb-2 text-gray-900">
              示例小說 {id}
            </h3>
            <p className="text-gray-600 text-sm mb-3">
              這是一個示例小說的描述，展示 Next.js 15 App Router 的卡片佈局...
            </p>
            <div className="flex items-center justify-between text-sm text-gray-500">
              <span>作者: 示例作者</span>
              <span>更新: 2025-01-01</span>
            </div>
            <Link 
              href={`/novels/${id}`}
              className="mt-3 block w-full text-center bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              閱讀小說
            </Link>
          </div>
        ))}
      </div>
    </div>
  )
}
