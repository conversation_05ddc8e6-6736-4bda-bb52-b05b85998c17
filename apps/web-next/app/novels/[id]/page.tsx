import { Metadata } from 'next'
import Link from 'next/link'
import { notFound } from 'next/navigation'

interface Props {
  params: {
    id: string
  }
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { id } = params
  
  return {
    title: `小說 ${id} - 詳情頁`,
    description: `查看小說 ${id} 的詳細資訊和章節列表`,
  }
}

export default function NovelDetailPage({ params }: Props) {
  const { id } = params
  
  // 簡單驗證 ID
  if (!id || isNaN(Number(id))) {
    notFound()
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">小說 {id} - 詳情頁</h1>
        <div className="flex gap-4">
          <Link 
            href="/novels"
            className="text-blue-600 hover:text-blue-800 transition-colors"
          >
            ← 返回列表
          </Link>
          <Link 
            href="/"
            className="text-blue-600 hover:text-blue-800 transition-colors"
          >
            首頁
          </Link>
        </div>
      </div>

      <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
        <h2 className="text-xl font-semibold mb-3 text-blue-900">
          🚧 動態路由展示
        </h2>
        <p className="text-blue-800 mb-4">
          這是 Next.js 15 App Router 的動態路由頁面，展示如何處理動態參數。
        </p>
        <div className="text-sm text-blue-700 space-y-1">
          <p>• 當前小說 ID: <code className="bg-blue-100 px-1 rounded">{id}</code></p>
          <p>• 路由路徑: <code className="bg-blue-100 px-1 rounded">/novels/[id]</code></p>
          <p>• 支援 generateMetadata 動態 SEO</p>
          <p>• 未來將整合 Django API 獲取真實數據</p>
        </div>
      </div>

      <div className="grid lg:grid-cols-3 gap-6">
        {/* 小說資訊 */}
        <div className="lg:col-span-2 space-y-6">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex gap-6">
              <div className="w-32 h-48 bg-gray-200 rounded-lg flex items-center justify-center">
                <span className="text-gray-500 text-sm">封面圖片</span>
              </div>
              <div className="flex-1 space-y-3">
                <h2 className="text-2xl font-bold text-gray-900">示例小說 {id}</h2>
                <p className="text-gray-600">
                  這是一個示例小說的詳細描述。在實際應用中，這裡會顯示從 Django API 
                  獲取的真實小說資訊，包括作者、分類、標籤、更新狀態等。
                </p>
                <div className="flex flex-wrap gap-2">
                  <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">玄幻</span>
                  <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-sm">連載中</span>
                  <span className="bg-purple-100 text-purple-800 px-2 py-1 rounded text-sm">熱門</span>
                </div>
                <div className="text-sm text-gray-500 space-y-1">
                  <p>作者: 示例作者</p>
                  <p>字數: 1,234,567 字</p>
                  <p>更新時間: 2025-01-01</p>
                </div>
              </div>
            </div>
          </div>

          {/* 章節列表 */}
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h3 className="text-xl font-semibold mb-4 text-gray-900">章節列表</h3>
            <div className="space-y-2">
              {Array.from({ length: 10 }, (_, i) => i + 1).map((chapter) => (
                <Link
                  key={chapter}
                  href={`/novels/${id}/chapters/${chapter}`}
                  className="block p-3 rounded-lg hover:bg-gray-50 transition-colors border"
                >
                  <div className="flex items-center justify-between">
                    <span className="font-medium text-gray-900">
                      第 {chapter} 章: 示例章節標題
                    </span>
                    <span className="text-sm text-gray-500">
                      2025-01-0{chapter % 9 + 1}
                    </span>
                  </div>
                </Link>
              ))}
            </div>
            <div className="mt-4 text-center">
              <button className="text-blue-600 hover:text-blue-800 transition-colors">
                載入更多章節...
              </button>
            </div>
          </div>
        </div>

        {/* 側邊欄 */}
        <div className="space-y-6">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h3 className="text-lg font-semibold mb-4 text-gray-900">操作</h3>
            <div className="space-y-3">
              <button className="w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition-colors">
                開始閱讀
              </button>
              <button className="w-full bg-gray-200 text-gray-800 py-2 rounded-lg hover:bg-gray-300 transition-colors">
                加入書架
              </button>
              <button className="w-full bg-gray-200 text-gray-800 py-2 rounded-lg hover:bg-gray-300 transition-colors">
                分享小說
              </button>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h3 className="text-lg font-semibold mb-4 text-gray-900">統計資訊</h3>
            <div className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">總點擊:</span>
                <span className="font-medium">123,456</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">收藏數:</span>
                <span className="font-medium">12,345</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">推薦票:</span>
                <span className="font-medium">1,234</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">評分:</span>
                <span className="font-medium">4.8/5.0</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
