import { APIClient, APIError } from '@/lib/api'

// Mock fetch
const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>

describe('API Error Handling', () => {
  beforeEach(() => {
    mockFetch.mockClear()
  })

  describe('APIError class', () => {
    it('should create error with correct properties', () => {
      const error = new APIError('Test error', 404, { detail: 'Not found' }, false)

      expect(error.message).toBe('Test error')
      expect(error.status).toBe(404)
      expect(error.data).toEqual({ detail: 'Not found' })
      expect(error.isRetryable).toBe(false)
      expect(error.name).toBe('APIError')
    })

    it('should determine retryable errors correctly', () => {
      // 5xx errors should be retryable
      expect(APIError.isRetryableError(500)).toBe(true)
      expect(APIError.isRetryableError(502)).toBe(true)
      expect(APIError.isRetryableError(503)).toBe(true)

      // Specific 4xx errors should be retryable
      expect(APIError.isRetryableError(408)).toBe(true) // Timeout
      expect(APIError.isRetryableError(429)).toBe(true) // Too Many Requests

      // Other 4xx errors should not be retryable
      expect(APIError.isRetryableError(400)).toBe(false)
      expect(APIError.isRetryableError(401)).toBe(false)
      expect(APIError.isRetryableError(403)).toBe(false)
      expect(APIError.isRetryableError(404)).toBe(false)
    })

    it('should provide user-friendly error messages', () => {
      const testCases = [
        { status: 400, expected: '請求參數錯誤，請檢查輸入內容' },
        { status: 401, expected: '未授權訪問，請重新登入' },
        { status: 403, expected: '權限不足，無法執行此操作' },
        { status: 404, expected: '請求的資源不存在' },
        { status: 408, expected: '請求超時，請稍後重試' },
        { status: 429, expected: '請求過於頻繁，請稍後重試' },
        { status: 500, expected: '伺服器內部錯誤，請稍後重試' },
        { status: 502, expected: '網關錯誤，請稍後重試' },
        { status: 503, expected: '服務暫時不可用，請稍後重試' },
        { status: 504, expected: '網關超時，請稍後重試' },
      ]

      testCases.forEach(({ status, expected }) => {
        const error = new APIError('Original message', status)
        expect(error.getUserFriendlyMessage()).toBe(expected)
      })
    })

    it('should fallback to original message for unknown status codes', () => {
      const error = new APIError('Custom error message', 418) // I'm a teapot
      expect(error.getUserFriendlyMessage()).toBe('Custom error message')
    })
  })

  describe('Retry logic', () => {
    it('should not retry 4xx client errors (except 408, 429)', async () => {
      const clientErrors = [400, 401, 403, 404, 422]

      for (const status of clientErrors) {
        mockFetch.mockResolvedValueOnce({
          ok: false,
          status,
          statusText: 'Client Error',
          headers: new Headers({ 'content-type': 'application/json' }),
          json: async () => ({ error: 'Client error' }),
        } as Response)

        await expect(APIClient.get('/test')).rejects.toThrow(APIError)

        // Should only call fetch once (no retries)
        expect(mockFetch).toHaveBeenCalledTimes(1)
        mockFetch.mockClear()
      }
    })

    it('should retry 5xx server errors', async () => {
      // First two calls fail, third succeeds
      mockFetch
        .mockResolvedValueOnce({
          ok: false,
          status: 500,
          statusText: 'Internal Server Error',
          headers: new Headers({ 'content-type': 'application/json' }),
          json: async () => ({ error: 'Server error' }),
        } as Response)
        .mockResolvedValueOnce({
          ok: false,
          status: 500,
          statusText: 'Internal Server Error',
          headers: new Headers({ 'content-type': 'application/json' }),
          json: async () => ({ error: 'Server error' }),
        } as Response)
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          statusText: 'OK',
          headers: new Headers({ 'content-type': 'application/json' }),
          json: async () => ({ success: true }),
        } as Response)

      const response = await APIClient.get('/test')

      expect(response.data).toEqual({ success: true })
      expect(mockFetch).toHaveBeenCalledTimes(3)
    })

    it('should retry timeout errors (408)', async () => {
      // First call times out, second succeeds
      mockFetch
        .mockResolvedValueOnce({
          ok: false,
          status: 408,
          statusText: 'Request Timeout',
          headers: new Headers({ 'content-type': 'application/json' }),
          json: async () => ({ error: 'Timeout' }),
        } as Response)
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          statusText: 'OK',
          headers: new Headers({ 'content-type': 'application/json' }),
          json: async () => ({ success: true }),
        } as Response)

      const response = await APIClient.get('/test')

      expect(response.data).toEqual({ success: true })
      expect(mockFetch).toHaveBeenCalledTimes(2)
    })

    it('should retry rate limit errors (429)', async () => {
      // First call rate limited, second succeeds
      mockFetch
        .mockResolvedValueOnce({
          ok: false,
          status: 429,
          statusText: 'Too Many Requests',
          headers: new Headers({ 'content-type': 'application/json' }),
          json: async () => ({ error: 'Rate limited' }),
        } as Response)
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          statusText: 'OK',
          headers: new Headers({ 'content-type': 'application/json' }),
          json: async () => ({ success: true }),
        } as Response)

      const response = await APIClient.get('/test')

      expect(response.data).toEqual({ success: true })
      expect(mockFetch).toHaveBeenCalledTimes(2)
    })

    it('should eventually give up after max retries', async () => {
      // All calls fail
      mockFetch.mockResolvedValue({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
        headers: new Headers({ 'content-type': 'application/json' }),
        json: async () => ({ error: 'Server error' }),
      } as Response)

      await expect(APIClient.get('/test')).rejects.toThrow(APIError)

      // Should call fetch 4 times (1 initial + 3 retries)
      expect(mockFetch).toHaveBeenCalledTimes(4)
    })
  })

  describe('Authentication headers', () => {
    it('should add Bearer token when provided', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Headers({ 'content-type': 'application/json' }),
        json: async () => ({ success: true }),
      } as Response)

      await APIClient.get('/test', {
        token: 'test-token-123'
      })

      expect(mockFetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          headers: expect.objectContaining({
            'Authorization': 'Bearer test-token-123'
          })
        })
      )
    })

    it('should add session cookie when provided', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Headers({ 'content-type': 'application/json' }),
        json: async () => ({ success: true }),
      } as Response)

      await APIClient.get('/test', {
        sessionCookie: 'sessionid=abc123; csrftoken=xyz789'
      })

      expect(mockFetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          headers: expect.objectContaining({
            'Cookie': 'sessionid=abc123; csrftoken=xyz789'
          })
        })
      )
    })

    it('should add SSR header when in server environment', async () => {
      // Mock server environment
      const originalWindow = global.window
      delete (global as any).window

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Headers({ 'content-type': 'application/json' }),
        json: async () => ({ success: true }),
      } as Response)

      await APIClient.get('/test', {
        isServerSide: true
      })

      expect(mockFetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          headers: expect.objectContaining({
            'X-Requested-With': 'SSR'
          })
        })
      )

      // Restore window
      global.window = originalWindow
    })
  })
})
