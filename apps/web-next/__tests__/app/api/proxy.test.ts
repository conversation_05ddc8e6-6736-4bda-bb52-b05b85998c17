/**
 * @jest-environment node
 */

import { NextRequest } from 'next/server'
import { GET, POST } from '@/app/api/proxy/[...path]/route'

// Mock fetch
global.fetch = jest.fn()
const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>

describe('/api/proxy/[...path]', () => {
  beforeEach(() => {
    mockFetch.mockClear()
  })

  describe('Path whitelist security', () => {
    it('should allow whitelisted paths', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Headers({ 'content-type': 'application/json' }),
        text: async () => JSON.stringify({ success: true }),
      } as Response)

      const request = new NextRequest('http://localhost:3001/api/proxy/novels')
      const response = await GET(request, { params: { path: ['novels'] } })

      expect(response.status).toBe(200)
      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/v1/novels',
        expect.any(Object)
      )
    })

    it('should allow whitelisted paths with IDs', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Headers({ 'content-type': 'application/json' }),
        text: async () => JSON.stringify({ success: true }),
      } as Response)

      const request = new NextRequest('http://localhost:3001/api/proxy/novels/123')
      const response = await GET(request, { params: { path: ['novels', '123'] } })

      expect(response.status).toBe(200)
      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/v1/novels/123',
        expect.any(Object)
      )
    })

    it('should block non-whitelisted paths', async () => {
      const request = new NextRequest('http://localhost:3001/api/proxy/admin/users')
      const response = await GET(request, { params: { path: ['admin', 'users'] } })
      const data = await response.json()

      expect(response.status).toBe(403)
      expect(data.error).toBe('Forbidden')
      expect(data.code).toBe('PATH_NOT_ALLOWED')
      expect(mockFetch).not.toHaveBeenCalled()
    })

    it('should block malicious paths', async () => {
      const maliciousPaths = [
        ['..', '..', 'etc', 'passwd'],
        ['internal', 'admin'],
        ['system', 'config'],
        ['database', 'dump'],
      ]

      for (const path of maliciousPaths) {
        const request = new NextRequest(`http://localhost:3001/api/proxy/${path.join('/')}`)
        const response = await GET(request, { params: { path } })
        const data = await response.json()

        expect(response.status).toBe(403)
        expect(data.error).toBe('Forbidden')
        expect(mockFetch).not.toHaveBeenCalled()
      }
    })
  })

  describe('Error handling', () => {
    it('should handle 401 unauthorized errors', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 401,
        statusText: 'Unauthorized',
        headers: new Headers({ 'content-type': 'application/json' }),
        text: async () => JSON.stringify({ error: 'Unauthorized' }),
      } as Response)

      const request = new NextRequest('http://localhost:3001/api/proxy/auth/profile')
      const response = await GET(request, { params: { path: ['auth', 'profile'] } })
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data.error).toBe('Unauthorized')
    })

    it('should handle 500 server errors', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
        headers: new Headers({ 'content-type': 'application/json' }),
        text: async () => JSON.stringify({ error: 'Internal Server Error' }),
      } as Response)

      const request = new NextRequest('http://localhost:3001/api/proxy/novels')
      const response = await GET(request, { params: { path: ['novels'] } })
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.error).toBe('Internal Server Error')
    })

    it('should handle network errors', async () => {
      mockFetch.mockRejectedValueOnce(new TypeError('Network error'))

      const request = new NextRequest('http://localhost:3001/api/proxy/novels')
      const response = await GET(request, { params: { path: ['novels'] } })
      const data = await response.json()

      expect(response.status).toBe(503)
      expect(data.error).toBe('Backend service unavailable')
    })

    it('should handle timeout errors', async () => {
      mockFetch.mockRejectedValueOnce(new DOMException('Timeout', 'AbortError'))

      const request = new NextRequest('http://localhost:3001/api/proxy/novels')
      const response = await GET(request, { params: { path: ['novels'] } })
      const data = await response.json()

      expect(response.status).toBe(504)
      expect(data.error).toBe('Request timeout')
    })
  })

  describe('HTTP methods', () => {
    it('should handle POST requests', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 201,
        statusText: 'Created',
        headers: new Headers({ 'content-type': 'application/json' }),
        text: async () => JSON.stringify({ id: 1, created: true }),
      } as Response)

      const request = new NextRequest('http://localhost:3001/api/proxy/novels', {
        method: 'POST',
        body: JSON.stringify({ title: 'Test Novel' }),
        headers: { 'Content-Type': 'application/json' },
      })

      const response = await POST(request, { params: { path: ['novels'] } })
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data.created).toBe(true)
      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/v1/novels',
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify({ title: 'Test Novel' }),
        })
      )
    })

    it('should preserve request headers', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Headers({ 'content-type': 'application/json' }),
        text: async () => JSON.stringify({ success: true }),
      } as Response)

      const request = new NextRequest('http://localhost:3001/api/proxy/auth/profile', {
        headers: {
          'Authorization': 'Bearer test-token',
          'X-API-Key': 'test-key',
        },
      })

      await GET(request, { params: { path: ['auth', 'profile'] } })

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/v1/auth/profile',
        expect.objectContaining({
          headers: expect.objectContaining({
            'authorization': 'Bearer test-token',
            'x-api-key': 'test-key',
          }),
        })
      )
    })
  })

  describe('CORS handling', () => {
    it('should handle OPTIONS requests', async () => {
      const request = new NextRequest('http://localhost:3001/api/proxy/novels', {
        method: 'OPTIONS',
      })

      // 需要導入 OPTIONS 處理器
      const { OPTIONS } = await import('@/app/api/proxy/[...path]/route')
      const response = await OPTIONS(request)

      expect(response.status).toBe(200)
      expect(response.headers.get('Access-Control-Allow-Origin')).toBe('*')
      expect(response.headers.get('Access-Control-Allow-Methods')).toContain('GET')
      expect(response.headers.get('Access-Control-Allow-Methods')).toContain('POST')
    })
  })
})
