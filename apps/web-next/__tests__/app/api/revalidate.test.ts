/**
 * @jest-environment node
 */

import { NextRequest } from 'next/server'
import { POST, GET } from '@/app/api/revalidate/route'

// Mock Next.js cache functions
jest.mock('next/cache', () => ({
  revalidatePath: jest.fn(),
  revalidateTag: jest.fn(),
}))

const { revalidatePath, revalidateTag } = require('next/cache')

describe('/api/revalidate', () => {
  const TEST_REVALIDATE_KEY = 'test-key-for-testing'
  
  beforeEach(() => {
    jest.clearAllMocks()
    // 設置測試環境變數
    process.env.REVALIDATE_SECRET = TEST_REVALIDATE_KEY
  })

  afterEach(() => {
    delete process.env.REVALIDATE_SECRET
  })

  describe('POST /api/revalidate', () => {
    it('should revalidate path with correct secret', async () => {
      const request = new NextRequest('http://localhost:3001/api/revalidate', {
        method: 'POST',
        body: JSON.stringify({
          secret: TEST_REVALIDATE_KEY,
          path: '/novels/1/chapters/1'
        }),
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.type).toBe('path')
      expect(data.message).toContain('/novels/1/chapters/1')
      expect(revalidatePath).toHaveBeenCalledWith('/novels/1/chapters/1')
    })

    it('should revalidate tag with correct secret', async () => {
      const request = new NextRequest('http://localhost:3001/api/revalidate', {
        method: 'POST',
        body: JSON.stringify({
          secret: TEST_REVALIDATE_KEY,
          tag: 'novels'
        }),
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.type).toBe('tag')
      expect(data.message).toContain('novels')
      expect(revalidateTag).toHaveBeenCalledWith('novels')
    })

    it('should reject invalid secret', async () => {
      const request = new NextRequest('http://localhost:3001/api/revalidate', {
        method: 'POST',
        body: JSON.stringify({
          secret: 'wrong-secret',
          path: '/novels/1'
        }),
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data.error).toBe('Invalid secret')
      expect(revalidatePath).not.toHaveBeenCalled()
    })

    it('should reject missing path and tag', async () => {
      const request = new NextRequest('http://localhost:3001/api/revalidate', {
        method: 'POST',
        body: JSON.stringify({
          secret: 'test-key-for-testing'
        }),
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toBe('Missing path or tag parameter')
    })

    it('should handle malformed JSON', async () => {
      const request = new NextRequest('http://localhost:3001/api/revalidate', {
        method: 'POST',
        body: 'invalid json',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.error).toBe('Internal server error')
    })
  })

  describe('GET /api/revalidate', () => {
    it('should revalidate path via GET request', async () => {
      const url = new URL('http://localhost:3001/api/revalidate')
      url.searchParams.set('secret', 'test-key-for-testing')
      url.searchParams.set('path', '/novels/1')

      const request = new NextRequest(url)
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.type).toBe('path')
      expect(revalidatePath).toHaveBeenCalledWith('/novels/1')
    })

    it('should return usage info when no parameters', async () => {
      const url = new URL('http://localhost:3001/api/revalidate')
      url.searchParams.set('secret', 'test-key-for-testing')

      const request = new NextRequest(url)
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.message).toBe('Next.js Revalidation API')
      expect(data.usage).toBeDefined()
      expect(data.examples).toBeDefined()
    })

    it('should reject GET request with invalid secret', async () => {
      const url = new URL('http://localhost:3001/api/revalidate')
      url.searchParams.set('secret', 'wrong-secret')
      url.searchParams.set('path', '/novels/1')

      const request = new NextRequest(url)
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data.error).toBe('Invalid secret')
    })
  })
})
