import { render, screen } from '@testing-library/react'
import HomePage from '@/app/page'

// Mock Next.js metadata
jest.mock('@/app/page', () => {
  const ActualHomePage = jest.requireActual('@/app/page').default
  return {
    __esModule: true,
    default: ActualHomePage,
    metadata: {
      title: '首頁',
      description: 'Next.js 15 App Router 首頁 - 與 CRA 應用並行運行',
    },
  }
})

describe('HomePage', () => {
  it('renders the main heading', () => {
    render(<HomePage />)

    const heading = screen.getByRole('heading', {
      name: /歡迎來到 Next\.js 15 App Router 版本/i,
    })

    expect(heading).toBeInTheDocument()
  })

  it('displays the hero section with correct content', () => {
    render(<HomePage />)

    // 檢查主標題
    expect(
      screen.getByText('歡迎來到 Next.js 15 App Router 版本')
    ).toBeInTheDocument()

    // 檢查描述文字
    expect(
      screen.getByText(/這是與現有 CRA 應用並行運行的 Next\.js 15 版本/)
    ).toBeInTheDocument()
  })

  it('displays feature cards', () => {
    render(<HomePage />)

    // 檢查功能卡片
    expect(screen.getByText('🚀 App Router')).toBeInTheDocument()
    expect(screen.getByText('⚡ 伺服器端渲染')).toBeInTheDocument()
    expect(screen.getByText('🔄 並行運行')).toBeInTheDocument()
  })

  it('displays system status section', () => {
    render(<HomePage />)

    // 檢查系統狀態
    expect(screen.getByText('系統狀態')).toBeInTheDocument()
    expect(screen.getByText('應用資訊')).toBeInTheDocument()
    expect(screen.getByText('Monorepo 整合')).toBeInTheDocument()
  })

  it('displays development information', () => {
    render(<HomePage />)

    // 檢查開發資訊
    expect(screen.getByText('開發資訊')).toBeInTheDocument()
    expect(screen.getByText(/當前端口: 3001/)).toBeInTheDocument()
    expect(screen.getByText(/CRA 應用端口: 3000/)).toBeInTheDocument()
    expect(screen.getByText(/Django 後端端口: 8000/)).toBeInTheDocument()
  })

  it('has navigation links', () => {
    render(<HomePage />)

    // 檢查導航連結
    const novelsLink = screen.getByRole('link', { name: /瀏覽小說/ })
    const aboutLink = screen.getByRole('link', { name: /了解更多/ })

    expect(novelsLink).toBeInTheDocument()
    expect(novelsLink).toHaveAttribute('href', '/novels')

    expect(aboutLink).toBeInTheDocument()
    expect(aboutLink).toHaveAttribute('href', '/about')
  })

  it('displays correct version information', () => {
    render(<HomePage />)

    // 檢查版本資訊
    expect(screen.getByText(/Next\.js 版本: 15\.1\.3/)).toBeInTheDocument()
    expect(screen.getByText(/React 版本: 18\.2\.0/)).toBeInTheDocument()
    expect(screen.getByText(/TypeScript 支援: ✅/)).toBeInTheDocument()
    expect(screen.getByText(/Tailwind CSS: ✅/)).toBeInTheDocument()
    expect(screen.getByText(/App Router: ✅/)).toBeInTheDocument()
  })

  it('displays monorepo integration status', () => {
    render(<HomePage />)

    // 檢查 Monorepo 整合狀態
    expect(screen.getByText(/pnpm workspace: ✅/)).toBeInTheDocument()
    expect(screen.getByText(/Turborepo 支援: ✅/)).toBeInTheDocument()
  })
})
