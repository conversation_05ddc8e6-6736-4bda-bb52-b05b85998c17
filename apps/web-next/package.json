{"name": "@novelwebsite/web-next", "version": "0.1.0", "private": true, "packageManager": "pnpm@9.4.0", "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "scripts": {"dev": "next dev -p 3001", "build": "next build", "start": "next start -p 3001", "lint": "next lint", "type-check": "tsc --noEmit", "clean": "rm -rf .next out coverage"}, "dependencies": {"next": "^15.1.3", "react": "^18.2.0", "react-dom": "^18.2.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/material": "^6.3.1", "axios": "^1.8.2", "clsx": "^2.1.0", "lodash": "^4.17.21", "styled-components": "^6.1.8"}, "devDependencies": {"@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/lodash": "^4.17.17", "@types/styled-components": "^5.1.34", "typescript": "^5.0.0", "eslint": "^8.57.0", "eslint-config-next": "^15.1.3", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@tailwindcss/typography": "^0.5.10"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}