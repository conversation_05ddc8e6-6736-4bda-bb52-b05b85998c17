// ESLint 9 平面配置格式 - Next.js 15 應用
const { FlatCompat } = require("@eslint/eslintrc");
const path = require("path");

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  // Next.js core web vitals 配置
  ...compat.extends("next/core-web-vitals", "next/typescript"),

  {
    // 忽略模式
    ignores: [
      ".next/",
      "out/",
      "node_modules/",
      "coverage/"
    ]
  },

  {
    // 自定義規則
    rules: {
      "@next/next/no-img-element": "off",
      "react/no-unescaped-entities": "off",
      "@typescript-eslint/no-unused-vars": "warn",
      "@typescript-eslint/no-explicit-any": "warn"
    },

    // ESLint 9 的新配置選項 (替代已廢棄的 reportUnusedDisableDirectives)
    linterOptions: {
      reportUnusedDisableDirectives: "warn"
    }
  }
];

module.exports = eslintConfig;
